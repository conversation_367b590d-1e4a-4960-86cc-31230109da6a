<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="GigGenius - Professional freelancer profile and portfolio">
    <title>GigGenius - Profile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #2563eb;
            --primary-pink: #d41b8c;
            --text-light: #ffffff;
            --text-dark: #333333;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-400: #ced4da;
            --gray-500: #adb5bd;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
            --border-radius-sm: 4px;
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 20px;
            --transition-fast: 0.2s ease;
            --transition: 0.3s ease;
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: var(--gray-100);
            color: var(--text-dark);
            line-height: 1.5;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        ul {
            list-style: none;
        }

        button, input, select, textarea {
            font-family: inherit;
            font-size: inherit;
            outline: none;
        }

        button {
            cursor: pointer;
            border: none;
            background: none;
        }

        img, video {
            max-width: 100%;
            height: auto;
        }

        /* Layout */
        .container {
            max-width: 2000px;
            margin: 0 auto;
            background-color: white;
            box-shadow: var(--shadow-sm);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            padding: 1.5rem 1rem 3rem;
            flex: 1;
        }

        /* Header Styles */
        .header {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
        }

        .header-content {
            display: flex;
            align-items: center;
            height: 64px;
            padding: 0 1.5rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-right: 2rem;
        }

        .logo-image {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .logo-text {
            font-size: 1.25rem;
            font-weight: bold;
            background: linear-gradient(135deg, #8b5cf6, #d946ef);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: none;
            align-items: center;
            gap: 2rem;
            flex: 1;
        }

        @media (min-width: 1024px) {
            .nav-links {
                display: flex;
            }
        }

        .nav-links a {
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--primary-blue);
            transition: color 0.2s;
            white-space: nowrap;
        }

        .nav-links a:hover {
            color: #1d4ed8;
        }

        /* Dropdown styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-trigger {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--primary-blue);
        }

        .dropdown-trigger i {
            font-size: 0.75rem;
            transition: transform 0.2s;
        }

        .dropdown:hover .dropdown-trigger i {
            transform: rotate(180deg);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: white;
            min-width: 180px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 4px;
            padding: 0.5rem 0;
            z-index: 100;
            display: none;
        }

        .dropdown:hover .dropdown-menu {
            display: block;
        }

        .dropdown-menu a {
            display: block;
            padding: 0.5rem 1rem;
            margin: 0;
            color: var(--primary-blue);
            font-size: 0.875rem;
        }

        .dropdown-menu a:hover {
            background-color: #f5f5f5;
            color: #1d4ed8;
        }

        /* Header actions styles */
        .header-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-left: auto;
        }

        .gigs-btn {
            display: none;
            padding: 0.5rem 1rem;
            border: 1px solid var(--primary-blue);
            color: var(--primary-blue);
            background: white;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        @media (min-width: 768px) {
            .gigs-btn {
                display: block;
            }
        }

        .gigs-btn:hover {
            background-color: #eff6ff;
        }

        .search-wrapper {
            position: relative;
            display: none;
        }

        @media (min-width: 768px) {
            .search-wrapper {
                display: block;
            }
        }

        .header-search {
            padding: 0.5rem 1rem;
            padding-right: 2.5rem;
            border: 1px solid var(--primary-blue);
            border-radius: 6px;
            font-size: 0.875rem;
            width: 256px;
        }

        .header-search:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
        }

        .search-wrapper i {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-blue);
            font-size: 0.875rem;
        }

        .profile-dropdown {
            position: relative;
        }

        .profile-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            overflow: hidden;
            display: none;
            cursor: pointer;
            border: 2px solid var(--primary-blue);
        }

        @media (min-width: 768px) {
            .profile-icon {
                display: block;
            }
        }

        .profile-icon img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: #000;
            min-width: 180px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.3);
            border-radius: 4px;
            padding: 0.5rem 0;
            z-index: 100;
            display: none;
            margin-top: 0.5rem;
        }

        .profile-dropdown-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #fff;
            font-size: 0.875rem;
            transition: background-color 0.2s;
            text-decoration: none;
        }

        .profile-dropdown-menu a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--primary-blue);
        }

        .profile-dropdown-menu.active {
            display: block;
        }

        .notification-btn {
            position: relative;
            background: none;
            border: none;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
        }

        .notification-btn:hover {
            background-color: #eff6ff;
        }

        .notification-badge {
            position: absolute;
            -top: 4px;
            -right: 4px;
            width: 20px;
            height: 20px;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            font-size: 0.75rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Notification Dropdown Styles */
        .notification-dropdown {
            position: relative;
        }

        .notification-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: white;
            min-width: 320px;
            max-height: 400px;
            overflow-y: auto;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 0;
            z-index: 100;
            display: none;
            margin-top: 0.5rem;
            border: 1px solid #e5e7eb;
        }

        .notification-dropdown-menu.active {
            display: block;
        }

        .notification-header {
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-header h3 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: #333;
        }

        .notification-header .mark-all-read {
            font-size: 0.75rem;
            color: #064dac;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }

        .notification-header .mark-all-read:hover {
            text-decoration: underline;
        }

        .notification-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .notification-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .notification-item:hover {
            background-color: #f9fafb;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item.unread {
            background-color: #f0f7ff;
        }

        .notification-item.unread:hover {
            background-color: #e1f0ff;
        }

        .notification-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .notification-icon i {
            color: #064dac;
            font-size: 1rem;
        }

        .notification-content {
            flex: 1;
        }

        .notification-text {
            font-size: 0.875rem;
            color: #333;
            margin-bottom: 0.25rem;
            line-height: 1.4;
        }

        .notification-text strong {
            color: #003a8c;
            font-weight: 600;
        }

        .notification-time {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .notification-footer {
            padding: 0.75rem 1rem;
            text-align: center;
            border-top: 1px solid #e5e7eb;
        }

        .notification-footer a {
            font-size: 0.875rem;
            color: #064dac;
            text-decoration: none;
        }

        .notification-footer a:hover {
            text-decoration: underline;
        }

        .no-notifications {
            padding: 2rem 1rem;
            text-align: center;
            color: #6b7280;
            font-size: 0.875rem;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            cursor: pointer;
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-blue);
        }

        @media (min-width: 1024px) {
            .mobile-menu-btn {
                display: none;
            }
        }

        .mobile-menu-btn:hover {
            background-color: #eff6ff;
        }

        .mobile-menu {
            display: none;
            position: absolute;
            top: 60px;
            right: 1rem;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 200px;
            z-index: 20;
            border: 1px solid #e5e7eb;
        }

        @media (min-width: 768px) {
            .mobile-menu {
                display: none !important;
            }
        }

        .mobile-menu.active {
            display: block;
        }

        .mobile-menu a {
            display: block;
            padding: 0.75rem 1rem;
            color: #333;
            font-size: 0.875rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .mobile-menu a:last-child {
            border-bottom: none;
        }

        .mobile-menu a:hover {
            background-color: #f9fafb;
        }

        /* Profile Section */
        .profile-section {
            margin-bottom: 2rem;
        }

        .profile-header {
            margin-bottom: 1rem;
        }

        .profile-name {
            font-size: 1.875rem;
            font-weight: bold;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .profile-title {
            font-size: 1.125rem;
            color: var(--gray-600);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .profile-title i {
            color: var(--primary-pink);
        }

        .profile-row {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        @media (min-width: 1024px) {
            .profile-row {
                flex-direction: row;
            }
        }

        .video-card, .profile-card {
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            width: 100%;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .video-card:hover, .profile-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        @media (min-width: 1024px) {
            .video-card, .profile-card {
                width: 50%;
            }
        }

        /* Video Container - Centered */
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
            overflow: hidden;
            background-color: #000;
        }

        .video-container video {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .expand-btn {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
            z-index: 2;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.875rem;
        }

        .expand-btn:hover {
            background: rgba(0, 0, 0, 0.7);
        }

        .video-container.expanded {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            padding: 0;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .video-container.expanded video {
            width: 90%;
            height: 90%;
            object-fit: contain;
        }

        /* Profile Content */
        .profile-content {
            padding: 1.5rem;
        }

        .profile-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        @media (min-width: 768px) {
            .profile-stats {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        .stat-item {
            text-align: left;
        }

        .stat-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-pink);
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .profile-summary {
            margin-bottom: 1.5rem;
        }

        .profile-summary h3 {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .profile-summary h3 i {
            color: var(--primary-blue);
        }

        .summary-text {
            color: var(--gray-600);
            margin-bottom: 0.5rem;
        }

        .show-more {
            color: var(--primary-pink);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
        }

        .show-more:hover {
            text-decoration: underline;
        }

        .edit-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            color: var(--primary-pink);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            margin-left: 0.5rem;
            background: none;
            border: none;
        }

        .edit-btn:hover {
            text-decoration: underline;
        }

        .profile-fields {
            margin-bottom: 1.5rem;
        }

        .field-group {
            margin-bottom: 1rem;
        }

        .field-group label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.375rem;
            color: var(--gray-700);
        }

        .field-input {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            background-color: #f9fafb;
            color: #333;
        }

        .edit-profile-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.625rem 1.25rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
            justify-content: center;
        }

        .edit-profile-btn:hover {
            background-color: #b91c77;
        }

        /* Portfolio and Work History Section */
        .portfolio-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .section-row {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        @media (min-width: 1024px) {
            .section-row {
                flex-direction: row;
            }
        }

        .section-card {
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            width: 100%;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        @media (min-width: 1024px) {
            .section-card {
                width: 50%;
            }
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section-title i {
            color: var(--primary-blue);
        }

        .section-content {
            padding: 1.25rem;
        }

        .introduction-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .textarea {
            width: 100%;
            min-height: 150px;
            padding: 0.75rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            resize: vertical;
            margin-bottom: 1.25rem;
            background-color: #f9fafb;
        }

        .section-actions {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .arrow-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: #f3f4f6;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s;
        }

        .arrow-btn:hover {
            background-color: #e5e7eb;
            color: #333;
        }

        .add-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .add-btn:hover {
            background-color: #b91c77;
        }

        .portfolio-image {
            width: 100%;
            height: 250px;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 1.25rem;
            background-color: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }

        .portfolio-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .portfolio-image:hover img {
            transform: scale(1.05);
        }

        /* New Portfolio Design */
        .portfolio-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding: 0 1.25rem;
        }

        .portfolio-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }

        .portfolio-actions {
            display: flex;
            gap: 0.5rem;
        }

        .portfolio-add-btn, .portfolio-refresh-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid var(--primary-blue);
            background: white;
            color: var(--primary-blue);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .portfolio-add-btn:hover, .portfolio-refresh-btn:hover {
            background: var(--primary-blue);
            color: white;
        }

        .portfolio-tabs {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
            padding: 0 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .portfolio-tab {
            background: none;
            border: none;
            padding: 0.75rem 0;
            font-size: 1rem;
            font-weight: 500;
            color: var(--gray-600);
            cursor: pointer;
            position: relative;
            transition: color 0.2s;
        }

        .portfolio-tab.active {
            color: var(--text-dark);
        }

        .portfolio-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--text-dark);
        }

        .portfolio-tab:hover {
            color: var(--text-dark);
        }

        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            padding: 0 1.25rem;
            margin-bottom: 2rem;
        }

        .portfolio-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }

        .portfolio-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        .portfolio-card-image {
            width: 100%;
            height: 200px;
            overflow: hidden;
        }

        .portfolio-card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }

        .portfolio-card:hover .portfolio-card-image img {
            transform: scale(1.05);
        }

        .portfolio-card-title {
            padding: 1rem;
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: var(--primary-green);
            line-height: 1.4;
        }

        .portfolio-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1.25rem;
        }

        .pagination-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 1px solid #e5e7eb;
            background: white;
            color: var(--gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 500;
        }

        .pagination-btn:hover {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .pagination-btn.active {
            background: var(--text-dark);
            color: white;
            border-color: var(--text-dark);
        }

        .pagination-dots {
            color: var(--gray-600);
            margin: 0 0.5rem;
        }

        .input-field {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
        }

        .action-buttons {
            display: flex;
            gap: 0.75rem;
            margin-top: 1rem;
        }

        .action-edit-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .action-edit-btn:hover {
            background-color: #1d4ed8;
        }

        .delete-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .delete-btn:hover {
            background-color: #b91c77;
        }

        .work-history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.25rem;
        }

        .work-history-title {
            display: inline-block;
            padding: 0.5rem 0.75rem;
            background-color: var(--primary-pink);
            color: white;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-card {
            background-color: white;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.25rem;
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .certification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .certification-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .certification-title {
            font-size: 1.125rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-title i {
            color: var(--primary-pink);
        }

        .certification-content {
            padding: 1.25rem;
        }

        .certification-detail {
            margin-bottom: 0.75rem;
        }

        .certification-detail strong {
            font-weight: 600;
        }

        .certification-description {
            margin-top: 1rem;
        }

        .certification-description h4 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .certification-description h4 i {
            color: var(--primary-blue);
        }

        .certification-description p {
            color: var(--gray-600);
        }

        /* Footer */
        footer {
            background: var(--primary-blue);
            padding: 2rem 5%;
            align-items: center;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 2rem;
            margin-bottom: 2rem;
        }

        @media (min-width: 768px) {
            .footer-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .footer-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        .footer-column h3 {
            margin-bottom: 1rem;
            color: var(--text-light);
            font-weight: 600;
        }

        .footer-column a {
            display: block;
            color: var(--text-light);
            text-decoration: none;
            margin-bottom: 0.5rem;
            transition: text-decoration 0.3s ease;
            opacity: 0.8;
        }

        .footer-column a:hover {
            text-decoration: underline;
            opacity: 1;
        }

        .footer-bottom {
            color: var(--text-light);
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }
        }

        .footer-bottom p {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 400;
        }

        .social-icons {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .social-icons a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            margin: 0;
        }

        .social-icons .bi {
            font-size: 1.2rem;
            color: var(--text-light);
            transition: transform 0.3s ease, color 0.3s ease;
        }

        .social-icons a:hover {
            background-color: var(--primary-pink);
            transform: translateY(-3px);
        }

        .footer-bottom a {
            color: var(--text-light);
            margin: 0 10px;
            text-decoration: none;
        }

        .footer-bottom a:hover {
            text-decoration: underline;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background-color: white;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            overflow-y: auto;
        }

        .modal-header {
            border-bottom: 1px solid #e5e7eb;
        }

        .modal-header-content {
            display: flex;
            align-items: center;
            height: 80px;
            padding: 0 2rem;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000;
            margin-bottom: 20px;
            text-align: center;
        }

        .modal-textarea {
            width: 100%;
            height: 300px;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 20px;
            outline: none;
            resize: none;
            font-size: 16px;
            font-family: inherit;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .modal-textarea:focus {
            border-color: #8b5cf6;
            box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
        }

        .modal-buttons {
            margin-top: 20px;
            text-align: center;
        }

        .modal-back-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: transparent;
            color: #000;
            text-decoration: underline;
        }

        .modal-back-btn:hover {
            color: #8b5cf6;
        }

        .modal-next-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            background-color: var(--primary-pink);
            color: #fff;
            margin-left: 10px;
        }

        .modal-next-btn:hover {
            background-color: #b91c77;
        }

        /* Edit Profile Modal */
        .profile-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        @media (min-width: 1024px) {
            .profile-form {
                flex-direction: row;
                align-items: flex-start;
            }
        }

        .profile-photo-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
        }

        .profile-photo {
            width: 150px;
            height: 150px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
            border-radius: 8px;
        }

        .profile-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .upload-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.375rem;
            padding: 0.5rem 0.75rem;
            background-color: #f3f4f6;
            color: #333;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .upload-btn:hover {
            background-color: #e5e7eb;
        }

        .upload-note {
            font-size: 0.75rem;
            color: #6b7280;
        }

        .profile-details-section {
            flex: 1;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.375rem;
        }

        .form-control {
            width: 100%;
            padding: 0.625rem;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-size: 0.875rem;
            background-color: #f9fafb;
            color: #333;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
        }

        .form-control[readonly] {
            background-color: #f3f4f6;
            cursor: not-allowed;
        }

        .form-row {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        @media (min-width: 640px) {
            .form-row {
                flex-direction: row;
            }
        }

        .form-col {
            flex: 1;
        }

        .next-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background-color: var(--primary-pink);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            width: 100%;
            margin-top: 1rem;
        }

        .next-btn:hover {
            background-color: #b91c77;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 1rem;
            }

            .main-content {
                padding: 1rem;
            }

            .profile-name {
                font-size: 1.5rem;
            }

            .profile-title {
                font-size: 1rem;
            }

            .modal-content {
                width: 95%;
                margin: 1rem;
            }

            .modal-body {
                padding: 1rem;
            }

            .profile-form {
                flex-direction: column;
            }
        }

        /* Utility Classes */
        .hidden {
            display: none;
        }

        .flex {
            display: flex;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        .gap-4 {
            gap: 1rem;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .mb-6 {
            margin-bottom: 1.5rem;
        }

        .text-center {
            text-align: center;
        }

        .font-bold {
            font-weight: bold;
        }

        .font-semibold {
            font-weight: 600;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .text-lg {
            font-size: 1.125rem;
        }

        .text-xl {
            font-size: 1.25rem;
        }

        .text-2xl {
            font-size: 1.5rem;
        }

        .text-3xl {
            font-size: 1.875rem;
        }

        .w-full {
            width: 100%;
        }

        .h-full {
            height: 100%;
        }

        .rounded {
            border-radius: 0.25rem;
        }

        .rounded-lg {
            border-radius: 0.5rem;
        }

        .shadow-sm {
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .shadow-md {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .transition-all {
            transition: all 0.3s ease;
        }

        .hover\:shadow-md:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .hover\:transform:hover {
            transform: translateY(-2px);
        }

        /* Star rating */
        .star-rating {
            color: #FBBF24;
        }

        /* Toggle switch */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #22C55E;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
            transform: translateX(20px);
        }

        /* Work History Styles */
        .work-history-summary {
            background-color: #f9fafb;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .work-history-summary h3 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .work-history-summary p {
            color: #374151;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .work-history-summary .show-more-btn {
            color: var(--primary-blue);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
        }

        .work-history-summary .ai-note {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.75rem;
        }

        .skills-section {
            margin-bottom: 1.5rem;
        }

        .skills-section h3 {
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .skill-tag {
            background-color: #f3f4f6;
            color: #374151;
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
        }

        .job-tabs {
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 1.5rem;
        }

        .job-tabs .tab-list {
            display: flex;
            gap: 1.5rem;
        }

        .job-tab {
            background: none;
            border: none;
            padding: 0.5rem 0;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
            cursor: pointer;
            position: relative;
            transition: color 0.2s;
        }

        .job-tab.active {
            color: #000;
        }

        .job-tab.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background: #000;
        }

        .job-list {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }

        .job-item {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1.5rem;
        }

        .job-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.25rem;
        }

        .job-title {
            color: var(--primary-blue);
            font-weight: 500;
            margin: 0;
        }

        .job-menu-btn {
            background: none;
            border: none;
            color: #6b7280;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .job-menu-btn:hover {
            background-color: #f3f4f6;
        }

        .job-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.25rem;
        }

        .job-rating .stars {
            display: flex;
            color: #FBBF24;
        }

        .job-rating .rating-score {
            font-weight: 500;
        }

        .job-rating .separator {
            color: #6b7280;
        }

        .job-date {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.75rem;
        }

        .job-feedback {
            font-size: 0.875rem;
            color: #374151;
            margin-bottom: 0.75rem;
        }

        .job-earnings {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .job-total {
            font-weight: 500;
        }

        .job-rate, .job-hours {
            font-size: 0.875rem;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <div class="header-content">
                <a href="{{ url_for('landing_page') }}" class="logo">
                    <div class="logo-image">
                        <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                    </div>
                    <span class="logo-text">GigGenius</span>
                </a>

                <nav class="nav-links">
                    <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                    <a href="{{ url_for('my_proposal') }}">Proposals</a>
                    <div class="dropdown">
                        <a href="#" class="dropdown-trigger">Contracts <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-menu">
                            <a href="{{ url_for('tracker') }}">Log Works</a>
                        </div>
                    </div>
                    <div class="dropdown">
                        <a href="#" class="dropdown-trigger">Earnings <i class="fas fa-chevron-down"></i></a>
                        <div class="dropdown-menu">
                            <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                            <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                            <a href="Tax_info.html">Tax Info</a>
                        </div>
                    </div>
                    <a href="#">Messages</a>
                </nav>

                <div class="header-actions">
                    <button class="gigs-btn">Gigs</button>

                    <div class="search-wrapper">
                        <input type="text" placeholder="Search..." class="header-search">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="notification-dropdown">
                        <button class="notification-btn" id="notificationBtn" aria-label="Notifications">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </button>
                        <div class="notification-dropdown-menu" id="notificationDropdown">
                            <div class="notification-header">
                                <h3>Notifications</h3>
                                <button class="mark-all-read" id="markAllRead">Mark all as read</button>
                            </div>
                            <ul class="notification-list">
                                <li class="notification-item unread">
                                    <div class="notification-icon">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-text">You have a new job offer from <strong>Tech Innovations Inc</strong></p>
                                        <span class="notification-time">2 hours ago</span>
                                    </div>
                                </li>
                                <li class="notification-item unread">
                                    <div class="notification-icon">
                                        <i class="fas fa-comment"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-text"><strong>John Smith</strong> sent you a message</p>
                                        <span class="notification-time">5 hours ago</span>
                                    </div>
                                </li>
                                <li class="notification-item">
                                    <div class="notification-icon">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-text">Payment of <strong>$250</strong> received from <strong>Global Marketing Solutions</strong></p>
                                        <span class="notification-time">Yesterday</span>
                                    </div>
                                </li>
                                <li class="notification-item">
                                    <div class="notification-icon">
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-text"><strong>Digital Transformation Co</strong> left you a 5-star review</p>
                                        <span class="notification-time">2 days ago</span>
                                    </div>
                                </li>
                                <li class="notification-item">
                                    <div class="notification-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="notification-content">
                                        <p class="notification-text">Your profile has been verified</p>
                                        <span class="notification-time">3 days ago</span>
                                    </div>
                                </li>
                            </ul>
                            <div class="notification-footer">
                                <a href="#">View all notifications</a>
                            </div>
                        </div>
                    </div>
                    <div class="profile-dropdown">
                        <div class="profile-icon" id="profileIcon">
                            <img src="{{ genius.profile_picture_url }}" alt="Profile">
                        </div>
                        <div class="profile-dropdown-menu" id="profileDropdown">
                            <a href="Genius_Profile.html">Your Profile</a>
                            <a href="{{ url_for('withdraw_earnings', section='profile-settings') }}">Settings</a>
                            <a href="#">Logout</a>
                        </div>
                    </div>
                    <button class="mobile-menu-btn" id="mobileMenuBtn" aria-label="Menu">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>

            <div class="mobile-menu" id="mobileMenu">
                <a href="{{ url_for('genius_page') }}">Find Gigs</a>
                <a href="{{ url_for('my_proposal') }}">Proposals</a>
                <a href="#">Contracts</a>
                <a href="{{ url_for('tracker') }}">Log Works</a>
                <a href="#">Earnings</a>
                <a href="{{ url_for('billing_and_earnings') }}">Billings and Earnings</a>
                <a href="{{ url_for('withdraw_earnings') }}">Withdraw Earnings</a>
                <a href="{{ url_for('tax_info') }}">Tax Info</a>
                <a href="#">Messages</a>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Profile Section -->
            <section class="profile-section">
                <!-- Name and Title above video -->
                <div class="profile-header">
                    <h1 class="profile-name" id="profileName">{{ genius.first_name }} {{ genius.last_name }}</h1>
                    <p class="profile-title">
                        <i class="fas fa-briefcase"></i>
                        <span id="profilePosition">{{ genius.position or 'Not specified' }}</span>
                    </p>
                </div>
                
                <div class="profile-row">
                    <!-- Video Card -->
                    <div class="video-card">
                        <div class="video-container" id="videoContainer">
                            <video id="profileVideo" controls poster="https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=450&fit=crop">
                                <source src="#" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                            <button class="expand-btn" id="expandBtn" aria-label="Expand video">
                                <i class="fas fa-expand"></i> Fullscreen
                            </button>
                        </div>
                    </div>

                    <!-- Profile Card -->
                    <div class="profile-card">
                        <div class="profile-content">
                            <!-- Stats - Updated to include 4 items -->
                            <div class="profile-stats">
                                <div class="stat-item">
                                    <div class="stat-value" id="hourlyRate">${{ genius.hourly_rate or '0' }}</div>
                                    <div class="stat-label">Hourly Rate</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="totalEarnings">$0</div>
                                    <div class="stat-label">Total Earnings</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="hired">0</div>
                                    <div class="stat-label">Hired</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="completedJobs">0</div>
                                    <div class="stat-label">Completed</div>
                                </div>
                            </div>

                            <!-- Professional Summary -->
                            <div class="profile-summary">
                                <div class="flex items-center justify-between mb-4">
                                    <h3><i class="fas fa-user-circle"></i> Professional Summary</h3>
                                    <button class="edit-btn" id="editSummaryBtn">
                                        <i class="fas fa-edit"></i>
                                        <span>Edit</span>
                                    </button>
                                </div>
                                <p class="summary-text" id="summaryText">{{ genius.professional_sum or 'Experienced full-stack developer with expertise in modern web technologies. Passionate about creating efficient, scalable solutions that drive business growth.' }}</p>
                                <span class="show-more hidden" id="showMore"><i class="fas fa-plus-circle"></i> Show More</span>
                            </div>

                            <!-- Profile Fields -->
                            <div class="profile-fields">
                                <div class="field-group">
                                    <label>Availability</label>
                                    <input type="text" value="{{ genius.availability or 'Full-time' }}" readonly class="field-input" id="availability">
                                </div>
                                <div class="field-group">
                                    <label>Language</label>
                                    <input type="text" value="English" readonly class="field-input" id="language">
                                </div>
                                <div class="field-group">
                                    <label>Country</label>
                                    <input type="text" value="{{ genius.country or 'Philippines' }}" readonly class="field-input" id="country">
                                </div>
                            </div>

                            <!-- Edit Profile Button -->
                            <button class="edit-profile-btn" id="editProfileBtn">
                                <i class="fas fa-user-edit"></i>
                                <span>Edit Profile</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Portfolio and Work History Section -->
            <section class="portfolio-section">
                <div class="section-row">
                    <!-- Introduction and Portfolio -->
                    <div class="section-card">
                        <div class="section-content">
                            <div class="introduction-header">
                                <h2 class="section-title" id="introduction"><i class="fas fa-info-circle"></i> Introduction</h2>
                                <button class="edit-btn" id="editIntroductionBtn">
                                    <i class="fas fa-edit"></i>
                                    <span>Edit</span>
                                </button>
                            </div>
                            <textarea class="textarea" id="introductionDisplay" placeholder="Write your introduction not less than 300 words..." readonly>{{ genius.introduction or 'No introduction provided yet. Click Edit to add one.' }}</textarea>
                        </div>

                        <!-- Portfolio Header -->
                        <div class="portfolio-header">
                            <h2 class="portfolio-title">Portfolio</h2>
                            <div class="portfolio-actions">
                                <button class="portfolio-add-btn" id="portfolioAddBtn">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="portfolio-refresh-btn">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Portfolio Tabs -->
                        <div class="portfolio-tabs">
                            <button class="portfolio-tab active" data-tab="published">Published</button>
                            <button class="portfolio-tab" data-tab="drafts">Drafts</button>
                        </div>

                        <!-- Portfolio Grid -->
                        <div class="portfolio-grid">
                            <div class="portfolio-card">
                                <div class="portfolio-card-image">
                                    <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=250&fit=crop" alt="Bathroom Remodeling">
                                </div>
                                <h3 class="portfolio-card-title">Bryan's Handyman & Construction LLC</h3>
                            </div>

                            <div class="portfolio-card">
                                <div class="portfolio-card-image">
                                    <img src="https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=250&fit=crop" alt="Virtual Assistant">
                                </div>
                                <h3 class="portfolio-card-title">Virtual Assistant</h3>
                            </div>

                            <div class="portfolio-card">
                                <div class="portfolio-card-image">
                                    <img src="https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=250&fit=crop" alt="Graphics Design">
                                </div>
                                <h3 class="portfolio-card-title">Graphics Design and Web Developer</h3>
                            </div>
                        </div>

                        <!-- Portfolio Pagination -->
                        <div class="portfolio-pagination">
                            <button class="pagination-btn prev">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="pagination-btn active">1</button>
                            <button class="pagination-btn">2</button>
                            <button class="pagination-btn">3</button>
                            <button class="pagination-btn">4</button>
                            <span class="pagination-dots">...</span>
                            <button class="pagination-btn next">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Work History -->
                    <div class="section-card">
                        <div class="section-content">
                            <h2 class="text-lg font-semibold mb-6">Work History</h2>

                            <!-- Work History Summary -->
                            <div class="work-history-summary">
                                <h3>
                                    Summary
                                    <label class="toggle-switch">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </h3>
                                <p>Carlo has successfully completed 15 projects with an average rating of 4.8 stars. His expertise spans web development, mobile applications, and digital marketing solutions.</p>
                                <p>Clients consistently praise his attention to detail, timely delivery, and excellent communication skills. He has generated over $25,000 in total earnings through the platform.</p>
                                <button class="show-more-btn">Show more</button>
                                <p class="ai-note">This summary was generated by AI based on work history and client feedback.</p>
                            </div>

                            <!-- Skills Used -->
                            <div class="skills-section">
                                <h3>Skills used in past work</h3>
                                <div class="skills-list">
                                    <span class="skill-tag">JavaScript</span>
                                    <span class="skill-tag">React</span>
                                    <span class="skill-tag">Node.js</span>
                                    <span class="skill-tag">Python</span>
                                    <span class="skill-tag">Digital Marketing</span>
                                    <span class="skill-tag">SEO</span>
                                    <span class="skill-tag">UI/UX Design</span>
                                    <span class="skill-tag">Database Design</span>
                                </div>
                            </div>

                            <!-- Job Tabs -->
                            <div class="job-tabs">
                                <div class="tab-list">
                                    <button class="job-tab active" data-tab="completed">Completed jobs (12)</button>
                                    <button class="job-tab" data-tab="in-progress">In progress (3)</button>
                                </div>
                            </div>

                            <!-- Job List -->
                            <div class="job-list">
                                <!-- Job Item 1 -->
                                <div class="job-item">
                                    <div class="job-header">
                                        <h4 class="job-title">E-commerce Website Development</h4>
                                        <button class="job-menu-btn">
                                            <i class="fas fa-ellipsis-h"></i>
                                        </button>
                                    </div>
                                    <div class="job-rating">
                                        <div class="stars">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <span class="rating-score">5.00</span>
                                        <span class="separator">•</span>
                                        <span>March 2024</span>
                                    </div>
                                    <div class="job-date">Completed on March 15, 2024</div>
                                    <div class="job-feedback">"Excellent work! Carlo delivered a fully functional e-commerce platform that exceeded our expectations. His attention to detail and communication throughout the project was outstanding."</div>
                                    <div class="job-earnings">
                                        <span class="job-total">$2,500.00 total</span>
                                        <div>
                                            <span class="job-rate">$50.00/hr</span>
                                            <span class="separator">•</span>
                                            <span class="job-hours">50 hours</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Job Item 2 -->
                                <div class="job-item">
                                    <div class="job-header">
                                        <h4 class="job-title">Mobile App UI/UX Design</h4>
                                        <button class="job-menu-btn">
                                            <i class="fas fa-ellipsis-h"></i>
                                        </button>
                                    </div>
                                    <div class="job-rating">
                                        <div class="stars">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                        </div>
                                        <span class="rating-score">4.90</span>
                                        <span class="separator">•</span>
                                        <span>February 2024</span>
                                    </div>
                                    <div class="job-date">Completed on February 28, 2024</div>
                                    <div class="job-feedback">"Great designer with modern aesthetic sense. The app design was exactly what we were looking for. Highly recommended!"</div>
                                    <div class="job-earnings">
                                        <span class="job-total">$1,800.00 total</span>
                                        <div>
                                            <span class="job-rate">$45.00/hr</span>
                                            <span class="separator">•</span>
                                            <span class="job-hours">40 hours</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Job Item 3 -->
                                <div class="job-item">
                                    <div class="job-header">
                                        <h4 class="job-title">Digital Marketing Campaign</h4>
                                        <button class="job-menu-btn">
                                            <i class="fas fa-ellipsis-h"></i>
                                        </button>
                                    </div>
                                    <div class="job-rating">
                                        <div class="stars">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="far fa-star"></i>
                                        </div>
                                        <span class="rating-score">4.70</span>
                                        <span class="separator">•</span>
                                        <span>January 2024</span>
                                    </div>
                                    <div class="job-date">Completed on January 20, 2024</div>
                                    <div class="job-feedback">"Professional approach to digital marketing. Increased our online presence significantly. Would work with Carlo again."</div>
                                    <div class="job-earnings">
                                        <span class="job-total">$1,200.00 total</span>
                                        <div>
                                            <span class="job-rate">$40.00/hr</span>
                                            <span class="separator">•</span>
                                            <span class="job-hours">30 hours</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer>
            <div class="footer-grid">
                <div class="footer-column">
                    <h3>For Clients</h3>
                    <a href="#">How to Hire</a>
                    <a href="#">Marketplace</a>
                    <a href="#">Payroll Services</a>
                    <a href="#">Service Catalog</a>
                    <a href="#">Business Networking</a>
                    <a href="#">PH Business Loan</a>
                </div>
                <div class="footer-column">
                    <h3>For Geniuses</h3>
                    <a href="#">How It Works?</a>
                    <a href="#">Why Can't I Apply?</a>
                    <a href="#">Direct Contracts</a>
                    <a href="#">Find Mentors</a>
                    <a href="#">Mentor Application</a>
                    <a href="#">PH Health Insurance</a>
                    <a href="#">PH Life Insurance</a>
                </div>
                <div class="footer-column">
                    <h3>Resources</h3>
                    <a href="#">Help & Support</a>
                    <a href="#">News & Events</a>
                    <a href="#">Affiliate Program</a>
                </div>
                <div class="footer-column">
                    <h3>Company</h3>
                    <a href="#">About Us</a>
                    <a href="#">Contact Us</a>
                    <a href="#">Charity Projects</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>
                    Follow Us:
                    <span class="social-icons">
                        <a href="https://www.facebook.com/giggenius.io"><i class="bi bi-facebook"></i></a>
                        <a href="https://www.instagram.com/giggenius.io/"><i class="bi bi-instagram"></i></a>
                        <a href="https://twitter.com/giggenius_io"><i class="bi bi-twitter-x"></i></a>
                        <a href="#"><i class="bi bi-tiktok"></i></a>
                        <a href="https://www.youtube.com/@giggenius"><i class="bi bi-youtube"></i></a>
                        <a href="https://www.linkedin.com/company/gig-genius/"><i class="bi bi-linkedin"></i></a>
                    </span>
                </p>
                <p>©2025 GigGenius by <a href="https://genuinelysolutions.com/">Genuinely Business Solutions</a></p>
                <p>
                    <a href="#">Terms of Service</a> |
                    <a href="#">Privacy Policy</a>
                </p>
            </div>
        </footer>
    </div>

    <!-- Modal for Professional Summary -->
    <div class="modal" id="professionalSummaryModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Professional Summary *</h2>
                <textarea class="modal-textarea" id="professionalSummaryTextarea" placeholder="Write something...">{{ genius.professional_sum or '' }}</textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="summaryBackBtn">Back</button>
                    <button class="modal-next-btn" id="summaryNextBtn">Next</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Introduction -->
    <div class="modal" id="introductionModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Introduction *</h2>
                <textarea class="modal-textarea" id="introductionTextarea" placeholder="Write something...">{{ genius.introduction or '' }}</textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="introBackBtn">Back</button>
                    <button class="modal-next-btn" id="introNextBtn">Next</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Edit Profile -->
    <div class="modal" id="editProfileModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <div class="modal-body">
                <h1 class="section-title">Welcome</h1>

                <div class="profile-form">
                    <!-- Profile Photo Section -->
                    <div class="profile-photo-section">
                        <div class="profile-photo">
                            <img src="{{ genius.profile_picture_url }}" alt="Profile Photo">
                        </div>
                        <label for="profile-upload" class="upload-btn">
                            <i class="fas fa-plus"></i>
                            <span>Upload</span>
                        </label>
                        <input type="file" id="profile-upload" accept="image/*" style="display: none;">
                        <p class="upload-note">Maximum of 2MB</p>
                    </div>

                    <!-- Profile Details Section -->
                    <div class="profile-details-section">
                        <h2 class="section-title">Edit your profile</h2>

                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" class="form-control" value="{{ genius.email or '' }}" readonly>
                        </div>

                        <div class="form-group">
                            <label for="mobile">Mobile No.</label>
                            <input type="tel" id="mobile" class="form-control" value="{{ genius.mobile or '' }}">
                        </div>

                        <div class="form-group">
                            <label for="position">Position</label>
                            <input type="text" id="position" class="form-control" value="{{ genius.position or '' }}">
                        </div>

                        <div class="form-row">
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="expertise">Expertise Level</label>
                                    <select id="expertise" class="form-control">
                                        <option value="Expert" {% if genius.expertise == 'Expert' %}selected{% endif %}>Expert</option>
                                        <option value="Intermediate" {% if genius.expertise == 'Intermediate' %}selected{% endif %}>Intermediate</option>
                                        <option value="Beginner" {% if genius.expertise == 'Beginner' %}selected{% endif %}>Beginner</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-col">
                                <div class="form-group">
                                    <label for="rate">Rate per Hour (USD)</label>
                                    <input type="number" id="rate" class="form-control" value="{{ genius.hourly_rate or '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="availability">Availability</label>
                            <input type="text" id="availability" class="form-control" value="{{ genius.availability or '' }}" placeholder="e.g., Part-Time, Full-Time, Weekends only, etc.">
                        </div>

                        <div class="form-group">
                            <label for="country">Country</label>
                            <select id="country" class="form-control">
                                <option value="">Select Country</option>
                                <option value="Nigeria" {% if genius.country == 'Nigeria' %}selected{% endif %}>Nigeria</option>
                                <option value="United States" {% if genius.country == 'United States' %}selected{% endif %}>United States</option>
                                <option value="United Kingdom" {% if genius.country == 'United Kingdom' %}selected{% endif %}>United Kingdom</option>
                                <option value="Canada" {% if genius.country == 'Canada' %}selected{% endif %}>Canada</option>
                                <option value="Australia" {% if genius.country == 'Australia' %}selected{% endif %}>Australia</option>
                                <option value="Germany" {% if genius.country == 'Germany' %}selected{% endif %}>Germany</option>
                                <option value="France" {% if genius.country == 'France' %}selected{% endif %}>France</option>
                                <option value="Japan" {% if genius.country == 'Japan' %}selected{% endif %}>Japan</option>
                                <option value="China" {% if genius.country == 'China' %}selected{% endif %}>China</option>
                                <option value="India" {% if genius.country == 'India' %}selected{% endif %}>India</option>
                                <option value="Brazil" {% if genius.country == 'Brazil' %}selected{% endif %}>Brazil</option>
                                <option value="South Africa" {% if genius.country == 'South Africa' %}selected{% endif %}>South Africa</option>
                                <!-- Add a fallback option for any other country value -->
                                {% if genius.country and genius.country not in ['Nigeria', 'United States', 'United Kingdom', 'Canada', 'Australia', 'Germany', 'France', 'Japan', 'China', 'India', 'Brazil', 'South Africa'] %}
                                <option value="{{ genius.country }}" selected>{{ genius.country }}</option>
                                {% endif %}
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="language">Language</label>
                            <select id="language" class="form-control">
                                <option value="English" selected>English</option>
                                <option value="French">French</option>
                                <option value="Spanish">Spanish</option>
                                <option value="German">German</option>
                                <option value="Chinese">Chinese</option>
                                <option value="Japanese">Japanese</option>
                                <option value="Arabic">Arabic</option>
                                <option value="Russian">Russian</option>
                                <option value="Portuguese">Portuguese</option>
                                <option value="Hindi">Hindi</option>
                            </select>
                        </div>

                        <div class="modal-buttons" style="text-align: right;">
                            <button class="modal-back-btn" id="profileBackBtn">Back</button>
                            <button class="modal-next-btn" id="saveProfileBtn">Next</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Portfolio Edit -->
    <div class="modal" id="portfolioEditModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Portfolio Project *</h2>
                <textarea class="modal-textarea" placeholder="Edit your project description..."></textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="portfolioBackBtn">Back</button>
                    <button class="modal-next-btn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Certification Edit -->
    <div class="modal" id="certificationEditModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Edit Certification *</h2>
                <textarea class="modal-textarea" placeholder="Edit your certification details..."></textarea>
                <div class="modal-buttons">
                    <button class="modal-back-btn" id="certificationBackBtn">Back</button>
                    <button class="modal-next-btn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Portfolio Add -->
    <div class="modal" id="portfolioAddModal">
        <div class="modal-content">
            <!-- Header Section -->
            <header class="modal-header">
                <div class="modal-header-content">
                    <div class="logo">
                        <div class="logo-image">
                            <img src="{{ url_for('static', filename='img/logo.png') }}" alt="GigGenius Logo">
                        </div>
                        <span class="logo-text">GigGenius</span>
                    </div>
                </div>
            </header>

            <!-- Form Section -->
            <div class="modal-body">
                <h2 class="modal-title">Add Portfolio Project *</h2>

                <div class="profile-form">
                    <!-- Project Image Section -->
                    <div class="profile-photo-section">
                        <div class="profile-photo">
                            <img id="portfolioPreview" src="https://via.placeholder.com/150x150?text=Project+Image" alt="Project Image">
                        </div>
                        <label for="portfolio-upload" class="upload-btn">
                            <i class="fas fa-image"></i>
                            <span>Upload Project Image</span>
                        </label>
                        <input type="file" id="portfolio-upload" accept="image/*" style="display: none;">
                        <p class="upload-note">Maximum of 2MB</p>
                    </div>

                    <!-- Portfolio Details Section -->
                    <div class="profile-details-section">
                        <h2 class="section-title">Project Details</h2>

                        <div class="form-group">
                            <label for="projectTitle">Project Title *</label>
                            <input type="text" id="projectTitle" class="form-control" placeholder="Enter project title" required>
                        </div>

                        <div class="form-group">
                            <label for="providerName">Provider Name *</label>
                            <input type="text" id="providerName" class="form-control" placeholder="Enter provider name" required>
                        </div>

                        <div class="form-group">
                            <label for="receiverName">Receiver Name *</label>
                            <input type="text" id="receiverName" class="form-control" placeholder="Enter receiver name" required>
                        </div>

                        <div class="form-group">
                            <label for="projectDate">Project Date *</label>
                            <input type="date" id="projectDate" class="form-control" required>
                        </div>

                        <div class="form-group">
                            <label for="projectDescription">Project Description *</label>
                            <textarea id="projectDescription" class="form-control" rows="6" placeholder="Write your project description (minimum 300 words)..." required style="min-height: 150px; resize: vertical;"></textarea>
                        </div>
                    </div>
                </div>

                <div class="modal-buttons">
                    <button class="modal-back-btn" id="portfolioAddBackBtn">Back</button>
                    <button class="modal-next-btn" id="portfolioAddSaveBtn">Save Project</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Show More functionality for summary text
        const summaryText = document.getElementById('summaryText');
        const showMoreBtn = document.getElementById('showMore');
        const editSummaryBtn = document.getElementById('editSummaryBtn');

        showMoreBtn.addEventListener('click', () => {
            summaryText.textContent = "Collapsible text is perfect for longer content like paragraphs and descriptions. It's a great way to give people more information while keeping your layout clean.";
            showMoreBtn.style.display = 'none';
            // Edit button is already visible
        });

        // Video expand/collapse functionality
        const videoContainer = document.getElementById('videoContainer');
        const expandBtn = document.getElementById('expandBtn');
        const video = document.getElementById('profileVideo');

        expandBtn.addEventListener('click', () => {
            videoContainer.classList.toggle('expanded');
            if (videoContainer.classList.contains('expanded')) {
                expandBtn.innerHTML = '<i class="fas fa-compress"></i> Exit Fullscreen';
                document.body.style.overflow = 'hidden';
            } else {
                expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                document.body.style.overflow = '';
            }
        });

        // Close expanded video when clicking outside
        document.addEventListener('click', (event) => {
            if (videoContainer.classList.contains('expanded') &&
                !video.contains(event.target) &&
                !expandBtn.contains(event.target)) {
                videoContainer.classList.remove('expanded');
                expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                document.body.style.overflow = '';
            }
        });

        // Close expanded video when pressing Escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && videoContainer.classList.contains('expanded')) {
                videoContainer.classList.remove('expanded');
                expandBtn.innerHTML = '<i class="fas fa-expand"></i> Fullscreen';
                document.body.style.overflow = '';
            }
        });

        // Modal functionality
        const professionalSummaryModal = document.getElementById('professionalSummaryModal');
        const introductionModal = document.getElementById('introductionModal');
        const editProfileModal = document.getElementById('editProfileModal');
        const portfolioEditModal = document.getElementById('portfolioEditModal');
        const certificationEditModal = document.getElementById('certificationEditModal');

        // Edit buttons
        const editSummary = document.getElementById('editSummaryBtn');
        const editIntroduction = document.getElementById('editIntroductionBtn');
        const editProfile = document.getElementById('editProfileBtn');
        const portfolioEdit = document.getElementById('portfolioEditBtn');
        const certificationEdit1 = document.getElementById('certificationEditBtn1');
        const certificationEdit2 = document.getElementById('certificationEditBtn2');

        // Back buttons
        const summaryBackBtn = document.getElementById('summaryBackBtn');
        const introBackBtn = document.getElementById('introBackBtn');
        const profileBackBtn = document.getElementById('profileBackBtn');
        const portfolioBackBtn = document.getElementById('portfolioBackBtn');
        const certificationBackBtn = document.getElementById('certificationBackBtn');

        // Open modals
        editSummary.addEventListener('click', () => {
            professionalSummaryModal.classList.add('active');
        });

        editIntroduction.addEventListener('click', () => {
            introductionModal.classList.add('active');
        });

        editProfile.addEventListener('click', () => {
            editProfileModal.classList.add('active');
        });

        portfolioEdit.addEventListener('click', () => {
            portfolioEditModal.classList.add('active');
        });

        certificationEdit1.addEventListener('click', () => {
            certificationEditModal.classList.add('active');
        });

        certificationEdit2.addEventListener('click', () => {
            certificationEditModal.classList.add('active');
        });

        // Close modals
        summaryBackBtn.addEventListener('click', () => {
            professionalSummaryModal.classList.remove('active');
        });

        introBackBtn.addEventListener('click', () => {
            introductionModal.classList.remove('active');
        });

        profileBackBtn.addEventListener('click', () => {
            editProfileModal.classList.remove('active');
        });

        portfolioBackBtn.addEventListener('click', () => {
            portfolioEditModal.classList.remove('active');
        });

        certificationBackBtn.addEventListener('click', () => {
            certificationEditModal.classList.remove('active');
        });

        // Close modals when clicking outside
        window.addEventListener('click', (event) => {
            if (event.target === professionalSummaryModal) {
                professionalSummaryModal.classList.remove('active');
            }
            if (event.target === introductionModal) {
                introductionModal.classList.remove('active');
            }
            if (event.target === editProfileModal) {
                editProfileModal.classList.remove('active');
            }
            if (event.target === portfolioEditModal) {
                portfolioEditModal.classList.remove('active');
            }
            if (event.target === certificationEditModal) {
                certificationEditModal.classList.remove('active');
            }
        });

        // Preview uploaded image
        const profileUpload = document.getElementById('profile-upload');
        const profilePhoto = document.querySelector('.profile-photo img');

        profileUpload.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    profilePhoto.src = e.target.result;
                }
                reader.readAsDataURL(file);
            }
        });

        // Mobile Menu Toggle
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.getElementById('mobileMenu');

        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('active');
                if (mobileMenu.classList.contains('active')) {
                    mobileMenuBtn.innerHTML = '<i class="fas fa-times"></i>';
                } else {
                    mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (event) => {
                if (!mobileMenuBtn.contains(event.target) && !mobileMenu.contains(event.target)) {
                    mobileMenu.classList.remove('active');
                    mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - Initializing modals...');

            // Debug: Log the genius data
            console.log('Genius country value:', '{{ genius.country }}');
            console.log('Genius data:', {
                country: '{{ genius.country }}',
                availability: '{{ genius.availability }}',
                expertise: '{{ genius.expertise }}',
                position: '{{ genius.position }}'
            });

            // Modal functionality
            const professionalSummaryModal = document.getElementById('professionalSummaryModal');
            const introductionModal = document.getElementById('introductionModal');
            const editProfileModal = document.getElementById('editProfileModal');
            const portfolioEditModal = document.getElementById('portfolioEditModal');
            const certificationEditModal = document.getElementById('certificationEditModal');
            const portfolioAddModal = document.getElementById('portfolioAddModal');

            // Edit buttons
            const editSummary = document.getElementById('editSummaryBtn');
            const editIntroduction = document.getElementById('editIntroductionBtn');
            const editProfile = document.getElementById('editProfileBtn');
            const portfolioEdit = document.getElementById('portfolioEditBtn');
            const portfolioAdd = document.getElementById('portfolioAddBtn');
            const certificationEdit1 = document.getElementById('certificationEditBtn1');
            const certificationEdit2 = document.getElementById('certificationEditBtn2');

            // Back buttons
            const summaryBackBtn = document.getElementById('summaryBackBtn');
            const introBackBtn = document.getElementById('introBackBtn');
            const profileBackBtn = document.getElementById('profileBackBtn');
            const portfolioBackBtn = document.getElementById('portfolioBackBtn');
            const portfolioAddBackBtn = document.getElementById('portfolioAddBackBtn');
            const certificationBackBtn = document.getElementById('certificationBackBtn');

            console.log('Modal elements found:', {
                professionalSummaryModal: !!professionalSummaryModal,
                editSummary: !!editSummary,
                editIntroduction: !!editIntroduction,
                editProfile: !!editProfile
            });

            // Close modals function
            function closeModal(modal) {
                if (modal) {
                    modal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            }

            // Open modals - with null checks and debugging
            if (editSummary && professionalSummaryModal) {
                editSummary.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Edit Summary clicked');
                    professionalSummaryModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (editIntroduction && introductionModal) {
                editIntroduction.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Edit Introduction clicked');
                    introductionModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (editProfile && editProfileModal) {
                editProfile.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Edit Profile clicked');
                    editProfileModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (portfolioEdit && portfolioEditModal) {
                portfolioEdit.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Portfolio Edit clicked');
                    portfolioEditModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (portfolioAdd && portfolioAddModal) {
                portfolioAdd.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Portfolio Add clicked');
                    portfolioAddModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (certificationEdit1 && certificationEditModal) {
                certificationEdit1.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Certification Edit 1 clicked');
                    certificationEditModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            if (certificationEdit2 && certificationEditModal) {
                certificationEdit2.addEventListener('click', (e) => {
                    e.preventDefault();
                    console.log('Certification Edit 2 clicked');
                    certificationEditModal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            }

            // Close modals with back buttons
            if (summaryBackBtn) {
                summaryBackBtn.addEventListener('click', () => {
                    console.log('Summary back button clicked');
                    closeModal(professionalSummaryModal);
                });
            }

            if (introBackBtn) {
                introBackBtn.addEventListener('click', () => {
                    console.log('Intro back button clicked');
                    closeModal(introductionModal);
                });
            }

            if (profileBackBtn) {
                profileBackBtn.addEventListener('click', () => {
                    console.log('Profile back button clicked');
                    closeModal(editProfileModal);
                });
            }

            if (portfolioBackBtn) {
                portfolioBackBtn.addEventListener('click', () => {
                    console.log('Portfolio back button clicked');
                    closeModal(portfolioEditModal);
                });
            }

            if (portfolioAddBackBtn) {
                portfolioAddBackBtn.addEventListener('click', () => {
                    console.log('Portfolio add back button clicked');
                    closeModal(portfolioAddModal);
                });
            }

            if (certificationBackBtn) {
                certificationBackBtn.addEventListener('click', () => {
                    console.log('Certification back button clicked');
                    closeModal(certificationEditModal);
                });
            }

            // Close modals when clicking outside
            window.addEventListener('click', (event) => {
                if (event.target === professionalSummaryModal) {
                    closeModal(professionalSummaryModal);
                }
                if (event.target === introductionModal) {
                    closeModal(introductionModal);
                }
                if (event.target === editProfileModal) {
                    closeModal(editProfileModal);
                }
                if (event.target === portfolioEditModal) {
                    closeModal(portfolioEditModal);
                }
                if (event.target === portfolioAddModal) {
                    closeModal(portfolioAddModal);
                }
                if (event.target === certificationEditModal) {
                    closeModal(certificationEditModal);
                }
            });

            // Close modals with Escape key
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape') {
                    closeModal(professionalSummaryModal);
                    closeModal(introductionModal);
                    closeModal(editProfileModal);
                    closeModal(portfolioEditModal);
                    closeModal(portfolioAddModal);
                    closeModal(certificationEditModal);
                }
            });

            // For mobile devices, make dropdown items clickable to expand
            const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');

            dropdownTriggers.forEach(trigger => {
                trigger.addEventListener('click', function(e) {
                    if (window.innerWidth < 768) {
                        e.preventDefault();
                        const dropdown = this.nextElementSibling;
                        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    const dropdownMenus = document.querySelectorAll('.dropdown-menu');
                    dropdownMenus.forEach(menu => {
                        if (window.innerWidth < 768) {
                            menu.style.display = 'none';
                        }
                    });
                }

                // Close profile dropdown when clicking outside
                if (!e.target.closest('.profile-dropdown')) {
                    const profileDropdown = document.getElementById('profileDropdown');
                    if (profileDropdown) {
                        profileDropdown.classList.remove('active');
                    }
                }

                // Close notification dropdown when clicking outside
                if (!e.target.closest('.notification-dropdown')) {
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown) {
                        notificationDropdown.classList.remove('active');
                    }
                }
            });

            // Profile dropdown toggle
            const profileIcon = document.getElementById('profileIcon');
            const profileDropdown = document.getElementById('profileDropdown');

            if (profileIcon && profileDropdown) {
                profileIcon.addEventListener('click', function(e) {
                    e.stopPropagation();
                    profileDropdown.classList.toggle('active');

                    // Close notification dropdown if open
                    const notificationDropdown = document.getElementById('notificationDropdown');
                    if (notificationDropdown && notificationDropdown.classList.contains('active')) {
                        notificationDropdown.classList.remove('active');
                    }
                });

                profileDropdown.addEventListener('click', (event) => {
                    event.stopPropagation();
                });
            }

            // Notification dropdown toggle
            const notificationBtn = document.getElementById('notificationBtn');
            const notificationDropdown = document.getElementById('notificationDropdown');
            const markAllReadBtn = document.getElementById('markAllRead');

            if (notificationBtn && notificationDropdown) {
                notificationBtn.addEventListener('click', function(event) {
                    event.stopPropagation();
                    notificationDropdown.classList.toggle('active');

                    // Close profile dropdown if open
                    if (profileDropdown && profileDropdown.classList.contains('active')) {
                        profileDropdown.classList.remove('active');
                    }
                });

                notificationDropdown.addEventListener('click', (event) => {
                    event.stopPropagation();
                });

                // Mark all notifications as read
                if (markAllReadBtn) {
                    markAllReadBtn.addEventListener('click', function() {
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        unreadNotifications.forEach(notification => {
                            notification.classList.remove('unread');
                        });

                        // Remove the notification indicator
                        const indicator = document.querySelector('.notification-indicator');
                        if (indicator) {
                            indicator.style.display = 'none';
                        }
                    });
                }

                // Mark individual notification as read when clicked
                const notificationItems = document.querySelectorAll('.notification-item');
                notificationItems.forEach(item => {
                    item.addEventListener('click', function() {
                        this.classList.remove('unread');

                        // Check if there are any unread notifications left
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');
                        if (unreadNotifications.length === 0) {
                            const indicator = document.querySelector('.notification-indicator');
                            if (indicator) {
                                indicator.style.display = 'none';
                            }
                        }
                    });
                });
            }

            // Professional Summary Next Button functionality
            const summaryNextBtn = document.getElementById('summaryNextBtn');
            const professionalSummaryTextarea = document.getElementById('professionalSummaryTextarea');

            if (summaryNextBtn && professionalSummaryTextarea) {
                summaryNextBtn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const professionalSummary = professionalSummaryTextarea.value.trim();

                    if (!professionalSummary) {
                        alert('Please enter a professional summary before saving.');
                        return;
                    }

                    // Show loading state
                    summaryNextBtn.disabled = true;
                    summaryNextBtn.textContent = 'Saving...';

                    try {
                        const response = await fetch('/update_professional_summary', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                professional_summary: professionalSummary
                            })
                        });

                        const data = await response.json();

                        if (data.success) {
                            // Update the display text
                            const summaryText = document.getElementById('summaryText');
                            if (summaryText) {
                                summaryText.textContent = professionalSummary;
                            }

                            // Close the modal
                            closeModal(professionalSummaryModal);

                            // Show success message
                            alert('Professional summary updated successfully!');
                        } else {
                            alert('Error: ' + (data.error || 'Failed to update professional summary'));
                        }
                    } catch (error) {
                        console.error('Error updating professional summary:', error);
                        alert('An error occurred while updating your professional summary. Please try again.');
                    } finally {
                        // Reset button state
                        summaryNextBtn.disabled = false;
                        summaryNextBtn.textContent = 'Next';
                    }
                });
            }

            // Introduction Next Button functionality
            const introNextBtn = document.getElementById('introNextBtn');
            const introductionTextarea = document.getElementById('introductionTextarea');

            if (introNextBtn && introductionTextarea) {
                introNextBtn.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const introduction = introductionTextarea.value.trim();
                    console.log('Introduction content:', introduction);

                    if (!introduction) {
                        alert('Please enter an introduction before saving.');
                        return;
                    }

                    // Show loading state
                    introNextBtn.disabled = true;
                    introNextBtn.textContent = 'Saving...';

                    try {
                        console.log('Sending request to /introduction');
                        const response = await fetch('/introduction', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                introduction: introduction
                            })
                        });

                        console.log('Response status:', response.status);
                        const data = await response.json();
                        console.log('Response data:', data);

                        if (data.success) {
                            // Update the display text
                            const introductionDisplay = document.getElementById('introductionDisplay');
                            if (introductionDisplay) {
                                introductionDisplay.value = introduction;
                            }

                            // Close the modal
                            closeModal(introductionModal);

                            // Show success message
                            alert('Introduction updated successfully!');
                        } else {
                            alert('Error: ' + (data.error || 'Failed to update introduction'));
                        }
                    } catch (error) {
                        console.error('Error updating introduction:', error);
                        alert('An error occurred while updating your introduction. Please try again.');
                    } finally {
                        // Reset button state
                        introNextBtn.disabled = false;
                        introNextBtn.textContent = 'Next';
                    }
                });
            }
            // Profile photo upload functionality
            const profileUpload = document.getElementById('profile-upload');
            const profilePhoto = document.querySelector('.profile-photo img');
            let selectedFile = null;

            if (profileUpload) {
                profileUpload.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        // Validate file type
                        if (!file.type.startsWith('image/')) {
                            alert('Please select an image file (JPEG, PNG, etc.)');
                            e.target.value = '';
                            return;
                        }

                        // Validate file size (2MB limit)
                        if (file.size > 2 * 1024 * 1024) {
                            alert('File size must be less than 2MB');
                            e.target.value = '';
                            return;
                        }

                        selectedFile = file;

                        // Preview the image
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            profilePhoto.src = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // Profile save functionality
            const saveProfileBtn = document.getElementById('saveProfileBtn');
            if (saveProfileBtn) {
                saveProfileBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Edit Profile clicked - saving profile data');

                    // Get form values
                    const email = document.getElementById('email').value.trim();
                    const mobile = document.getElementById('mobile').value.trim();
                    const position = document.getElementById('position').value.trim();
                    const expertise = document.getElementById('expertise').value;
                    const hourly_rate = document.getElementById('rate').value;
                    const availability = document.getElementById('availability').value;
                    const country = document.getElementById('country').value;
                    const language = document.getElementById('language').value;

                    // Validate required fields
                    if (!email || !mobile || !position || !expertise || !hourly_rate || !availability || !country) {
                        alert('Please fill in all required fields');
                        return;
                    }

                    // Validate email format
                    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (!emailPattern.test(email)) {
                        alert('Please enter a valid email address');
                        return;
                    }

                    // Validate hourly rate
                    if (isNaN(hourly_rate) || parseFloat(hourly_rate) < 0) {
                        alert('Please enter a valid hourly rate');
                        return;
                    }

                    try {
                        // Show loading state
                        saveProfileBtn.disabled = true;
                        saveProfileBtn.textContent = 'Saving...';

                        // Check if we have a profile photo to upload
                        if (selectedFile) {
                            // Use FormData for file upload
                            const formData = new FormData();
                            formData.append('email', email);
                            formData.append('mobile', mobile);
                            formData.append('position', position);
                            formData.append('expertise', expertise);
                            formData.append('hourly_rate', parseFloat(hourly_rate));
                            formData.append('availability', availability);
                            formData.append('country', country);
                            formData.append('language', language);
                            formData.append('profile_photo', selectedFile);

                            const response = await fetch('/update_genius_profile', {
                                method: 'POST',
                                body: formData
                            });

                            const data = await response.json();

                            if (data.success) {
                                alert('Profile updated successfully!');
                                closeModal(editProfileModal);
                                window.location.reload();
                            } else {
                                alert('Error: ' + (data.error || 'Failed to update profile'));
                            }
                        } else {
                            // Use JSON for regular data without file upload
                            const response = await fetch('/update_genius_profile', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    email: email,
                                    mobile: mobile,
                                    position: position,
                                    expertise: expertise,
                                    hourly_rate: parseFloat(hourly_rate),
                                    availability: availability,
                                    country: country,
                                    language: language
                                })
                            });

                            const data = await response.json();

                            if (data.success) {
                                alert('Profile updated successfully!');
                                closeModal(editProfileModal);
                                window.location.reload();
                            } else {
                                alert('Error: ' + (data.error || 'Failed to update profile'));
                            }
                        }
                    } catch (error) {
                        alert('An error occurred while saving your profile');
                        console.error('Profile update error:', error);
                    } finally {
                        saveProfileBtn.disabled = false;
                        saveProfileBtn.textContent = 'Next';
                    }
                });
            }

            // Portfolio image upload functionality
            const portfolioUpload = document.getElementById('portfolio-upload');
            const portfolioPreview = document.getElementById('portfolioPreview');
            let selectedPortfolioFile = null;

            if (portfolioUpload) {
                portfolioUpload.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        // Validate file type
                        if (!file.type.startsWith('image/')) {
                            alert('Please select an image file (JPEG, PNG, etc.)');
                            e.target.value = '';
                            return;
                        }

                        // Validate file size (2MB limit)
                        if (file.size > 2 * 1024 * 1024) {
                            alert('File size must be less than 2MB');
                            e.target.value = '';
                            return;
                        }

                        selectedPortfolioFile = file;

                        // Preview the image
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            portfolioPreview.src = e.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // Portfolio save functionality
            const portfolioAddSaveBtn = document.getElementById('portfolioAddSaveBtn');
            if (portfolioAddSaveBtn) {
                portfolioAddSaveBtn.addEventListener('click', async (e) => {
                    e.preventDefault();
                    console.log('Portfolio Add Save clicked');

                    // Get form values
                    const projectTitle = document.getElementById('projectTitle').value.trim();
                    const providerName = document.getElementById('providerName').value.trim();
                    const receiverName = document.getElementById('receiverName').value.trim();
                    const projectDate = document.getElementById('projectDate').value;
                    const projectDescription = document.getElementById('projectDescription').value.trim();

                    // Validate required fields
                    if (!projectTitle || !providerName || !receiverName || !projectDate || !projectDescription) {
                        alert('Please fill in all required fields');
                        return;
                    }

                    // Validate description length (minimum 300 words)
                    const wordCount = projectDescription.split(/\s+/).filter(word => word.length > 0).length;
                    if (wordCount < 300) {
                        alert(`Project description must be at least 300 words. Current word count: ${wordCount}`);
                        return;
                    }

                    // Validate image upload
                    if (!selectedPortfolioFile) {
                        alert('Please upload a project image');
                        return;
                    }

                    try {
                        // Show loading state
                        portfolioAddSaveBtn.disabled = true;
                        portfolioAddSaveBtn.textContent = 'Saving...';

                        // Use FormData for file upload
                        const formData = new FormData();
                        formData.append('project_title', projectTitle);
                        formData.append('provider_name', providerName);
                        formData.append('receiver_name', receiverName);
                        formData.append('project_date', projectDate);
                        formData.append('project_description', projectDescription);
                        formData.append('project_image', selectedPortfolioFile);

                        const response = await fetch('/add_portfolio_project', {
                            method: 'POST',
                            body: formData
                        });

                        const data = await response.json();

                        if (data.success) {
                            alert('Portfolio project added successfully!');

                            // Reset form
                            document.getElementById('projectTitle').value = '';
                            document.getElementById('providerName').value = '';
                            document.getElementById('receiverName').value = '';
                            document.getElementById('projectDate').value = '';
                            document.getElementById('projectDescription').value = '';
                            portfolioPreview.src = 'https://via.placeholder.com/150x150?text=Project+Image';
                            portfolioUpload.value = '';
                            selectedPortfolioFile = null;

                            closeModal(portfolioAddModal);

                            // Optionally reload the page to show the new portfolio item
                            // window.location.reload();
                        } else {
                            alert('Error: ' + (data.error || 'Failed to add portfolio project'));
                        }
                    } catch (error) {
                        alert('An error occurred while saving your portfolio project');
                        console.error('Portfolio add error:', error);
                    } finally {
                        portfolioAddSaveBtn.disabled = false;
                        portfolioAddSaveBtn.textContent = 'Save Project';
                    }
                });
            }

            // Portfolio Tabs functionality
            const portfolioTabs = document.querySelectorAll('.portfolio-tab');
            portfolioTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    portfolioTabs.forEach(t => t.classList.remove('active'));
                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Here you can add logic to show/hide different portfolio content
                    const tabType = this.getAttribute('data-tab');
                    console.log('Switched to tab:', tabType);
                });
            });

            // Portfolio Pagination functionality
            const paginationBtns = document.querySelectorAll('.pagination-btn');
            paginationBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (!this.classList.contains('prev') && !this.classList.contains('next')) {
                        // Remove active class from all pagination buttons
                        paginationBtns.forEach(b => b.classList.remove('active'));
                        // Add active class to clicked button
                        this.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html>


