<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GigGenius Profile</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <style>
    /* Custom styles */
    .bg-gradient-text {
      background: linear-gradient(to right, #064dac, #CD208B);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    
    .modal {
      display: none;
      position: fixed;
      z-index: 100;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      overflow: auto;
      background-color: rgba(0, 0, 0, 0.4);
    }
    
    .modal-content {
      background-color: #fefefe;
      margin: 10% auto;
      padding: 20px;
      border: 1px solid #888;
      border-radius: 0.5rem;
      width: 80%;
      max-width: 800px;
      max-height: 80vh;
      overflow-y: auto;
    }
    
    .close {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }
    
    .close:hover,
    .close:focus {
      color: black;
      text-decoration: none;
    }
    
    .dropdown {
      position: relative;
      display: inline-block;
    }
    
    .dropdown-content {
      display: none;
      position: absolute;
      background-color: #f9f9f9;
      min-width: 160px;
      box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
      z-index: 1;
      border-radius: 0.375rem;
    }
    
    .dropdown:hover .dropdown-content {
      display: block;
    }
    
    .tab-content {
      display: none;
    }
    
    .tab-content.active {
      display: block;
    }
    
    .avatar {
      position: relative;
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 9999px;
      overflow: hidden;
    }
    
    .avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .avatar-fallback {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: #e5e7eb;
      color: #6b7280;
      font-weight: 500;
    }
    
    .badge {
      display: inline-flex;
      align-items: center;
      border-radius: 9999px;
      padding: 0.25rem 0.75rem;
      font-size: 0.75rem;
      font-weight: 500;
    }
    
    .badge-sm {
      height: 1.25rem;
      width: 1.25rem;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .card {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      overflow: hidden;
    }
    
    .fullscreen-video {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: black;
      z-index: 50;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .fullscreen-video video {
      max-width: 100%;
      max-height: 100%;
    }
    
    .hidden {
      display: none;
    }
    
    .draggable-item {
      cursor: move;
    }
    
    /* Star rating */
    .star-rating {
      color: #FBBF24;
    }
    
    /* Toggle switch */
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 24px;
    }
    
    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #22C55E;
      transition: .4s;
      border-radius: 34px;
    }
    
    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
      transform: translateX(20px);
    }
    
    /* Loading Screen Styles */
    .loading-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #f9fafb;
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .spinner {
      border: 4px solid #f3f4f6;
      border-top: 4px solid #064dac;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .fade-in {
      animation: fadeIn 0.5s ease-in;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .page-enter {
      animation: pageEnter 0.5s ease-out;
    }
    
    @keyframes pageEnter {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .progress-bar {
      transition: width 0.3s ease;
    }
    
    /* Main content initially hidden */
    .main-content {
      display: none;
    }
    
    .main-content.show {
      display: block;
    }
  </style>
</head>
<body class="min-h-screen bg-gray-50">
  
  <!-- Loading Screen -->
  <div id="loadingScreen" class="loading-screen">
    <div class="text-center fade-in">
      <!-- Logo -->
      <div class="flex items-center justify-center gap-3 mb-8">
        <div class="w-16 h-16 bg-white rounded-full shadow-lg flex items-center justify-center">
          <span class="text-2xl font-bold bg-gradient-text">GG</span>
        </div>
        <span class="text-3xl font-bold bg-gradient-text">GigGenius</span>
      </div>
      
      <!-- Loading Spinner -->
      <div class="flex justify-center mb-6">
        <div class="spinner"></div>
      </div>
      
      <!-- Loading Text -->
      <p class="text-gray-600 text-lg mb-2">Loading your profile...</p>
      <p class="text-gray-500 text-sm">Please wait while we prepare your workspace</p>
      
      <!-- Progress Bar -->
      <div class="w-64 bg-gray-200 rounded-full h-2 mx-auto mt-6">
        <div id="progressBar" class="bg-gradient-to-r from-[#064dac] to-[#CD208B] h-2 rounded-full progress-bar" style="width: 0%"></div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div id="mainContent" class="main-content page-enter">
    <!-- Header -->
    <header class="sticky top-0 z-50 bg-white border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <div class="flex items-center gap-3">
            <div class="avatar h-10 w-10">
              <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Crect width='40' height='40' fill='%23e5e7eb'/%3E%3Ctext x='20' y='25' text-anchor='middle' fill='%236b7280' font-family='Arial' font-size='14' font-weight='500'%3EGG%3C/text%3E%3C/svg%3E" alt="GigGenius">
              <div class="avatar-fallback">GG</div>
            </div>
            <span class="text-xl font-bold bg-gradient-text">GigGenius</span>
          </div>

          <!-- Navigation -->
          <nav class="hidden lg:flex items-center gap-8">
            <a href="#" class="text-[#064dac] hover:text-opacity-80 font-medium">Find Gigs</a>
            <a href="#" class="text-[#064dac] hover:text-opacity-80 font-medium">Proposals</a>
            <div class="dropdown">
              <button class="flex items-center gap-1 text-[#064dac] hover:text-opacity-80 font-medium">
                Contracts <i class="fas fa-chevron-down text-xs"></i>
              </button>
              <div class="dropdown-content">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Log Works</a>
              </div>
            </div>
            <div class="dropdown">
              <button class="flex items-center gap-1 text-[#064dac] hover:text-opacity-80 font-medium">
                Earnings <i class="fas fa-chevron-down text-xs"></i>
              </button>
              <div class="dropdown-content">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Billings and Earnings</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Withdraw Earnings</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Tax Info</a>
              </div>
            </div>
            <a href="#" class="text-[#064dac] hover:text-opacity-80 font-medium">Messages</a>
          </nav>

          <!-- Header Actions -->
          <div class="flex items-center gap-4">
            <button class="hidden md:flex border border-[#064dac] text-[#064dac] hover:bg-[#064dac] hover:text-white px-4 py-2 rounded-md">
              Gigs
            </button>

            <div class="hidden md:flex relative">
              <input type="text" placeholder="Search..." class="w-64 pr-10 border border-[#064dac]/20 focus:border-[#064dac] rounded-md py-2 px-3">
              <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"></i>
            </div>

            <!-- Notifications -->
            <div class="relative">
              <button id="notificationBtn" class="p-2 rounded-full hover:bg-gray-100">
                <i class="fas fa-bell text-[#064dac]"></i>
                <span class="badge badge-sm absolute -top-1 -right-1 bg-[#CD208B] text-white">3</span>
              </button>

              <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50">
                <div class="p-4 border-b">
                  <div class="flex justify-between items-center">
                    <h3 class="font-semibold">Notifications</h3>
                    <button class="text-[#064dac] text-sm">Mark all as read</button>
                  </div>
                </div>
                <div class="max-h-96 overflow-y-auto">
                  <div class="p-3 border-b hover:bg-gray-50 cursor-pointer">
                    <div class="flex gap-3">
                      <div class="w-8 h-8 bg-[#064dac]/10 rounded-full flex items-center justify-center">
                        <i class="fas fa-briefcase h-4 w-4 text-[#064dac]"></i>
                      </div>
                      <div class="flex-1">
                        <p class="text-sm">
                          You have a new job offer from <strong>Tech Innovations Inc</strong>
                        </p>
                        <span class="text-xs text-gray-500">2 hours ago</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Profile Dropdown -->
            <div class="relative">
              <button id="profileBtn" class="p-1 rounded-full hover:bg-gray-100">
                <div class="avatar h-8 w-8 border-2 border-[#064dac]">
                  <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='150' height='150' viewBox='0 0 150 150'%3E%3Crect width='150' height='150' fill='%23e5e7eb'/%3E%3Ctext x='75' y='85' text-anchor='middle' fill='%236b7280' font-family='Arial' font-size='48' font-weight='500'%3EJD%3C/text%3E%3C/svg%3E" alt="Profile">
                  <div class="avatar-fallback">JD</div>
                </div>
              </button>

              <div id="profileDropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border z-50">
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Your Profile</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Settings</a>
                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Logout</a>
              </div>
            </div>

            <!-- Mobile Menu -->
            <button id="mobileMenuBtn" class="lg:hidden p-2 rounded-full hover:bg-gray-100">
              <i class="fas fa-bars text-[#064dac]"></i>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Profile Header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-[#064dac] mb-2">John Doe</h1>
        <p class="text-lg text-gray-600 flex items-center gap-2">
          <i class="fas fa-briefcase text-[#CD208B]"></i>
          Full Stack Developer
        </p>
      </div>

      <!-- Profile Content -->
      <div class="grid lg:grid-cols-2 gap-8 mb-8">
        <!-- Video Card -->
        <div class="card">
          <div id="videoContainer" class="relative aspect-video">
            <video id="profileVideo" class="w-full h-full object-cover" controls poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='400' viewBox='0 0 600 400'%3E%3Crect width='600' height='400' fill='%23e5e7eb'/%3E%3Ctext x='300' y='210' text-anchor='middle' fill='%236b7280' font-family='Arial' font-size='24'%3EVideo Placeholder%3C/text%3E%3C/svg%3E">
              <source src="#" type="video/mp4">
              Your browser does not support the video tag.
            </video>
            <button id="fullscreenBtn" class="absolute bottom-4 right-4 bg-white text-gray-800 px-3 py-1 rounded-md shadow-md flex items-center">
              <i class="fas fa-expand mr-2"></i>
              Fullscreen
            </button>
          </div>
        </div>

        <!-- Profile Stats Card -->
        <div class="card">
          <div class="p-6">
            <!-- Stats Grid -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div>
                <div class="text-xl font-bold text-[#CD208B]">$50</div>
                <div class="text-sm text-gray-600">Hourly Rate</div>
              </div>
              <div>
                <div class="text-xl font-bold text-[#CD208B]">$0</div>
                <div class="text-sm text-gray-600">Total Earnings</div>
              </div>
              <div>
                <div class="text-xl font-bold text-[#CD208B]">0</div>
                <div class="text-sm text-gray-600">Hired</div>
              </div>
              <div>
                <div class="text-xl font-bold text-[#CD208B]">0</div>
                <div class="text-sm text-gray-600">Completed</div>
              </div>
            </div>

            <!-- Professional Summary -->
            <div class="mb-6">
              <div class="flex items-center justify-between mb-3">
                <h3 class="font-semibold flex items-center gap-2">
                  <i class="fas fa-user text-[#064dac]"></i>
                  Professional Summary
                </h3>
                <button id="editSummaryBtn" class="text-[#CD208B] hover:text-opacity-80 text-sm flex items-center">
                  <i class="fas fa-edit mr-1"></i>
                  Edit
                </button>
              </div>
              <p id="professionalSummary" class="text-gray-600 text-sm">
                Experienced full-stack developer with expertise in modern web technologies. Passionate about creating efficient, scalable solutions that drive business growth.
              </p>
            </div>

            <!-- Profile Fields -->
            <div class="space-y-4 mb-6">
              <div>
                <label class="text-sm font-medium text-gray-700">Availability</label>
                <input type="text" value="Full-time" readonly class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md">
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700">Language</label>
                <input type="text" value="English" readonly class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md">
              </div>
              <div>
                <label class="text-sm font-medium text-gray-700">Country</label>
                <input type="text" value="United States" readonly class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md">
              </div>
            </div>

            <!-- Edit Profile Button -->
            <button id="editProfileBtn" class="w-full bg-[#CD208B] hover:bg-opacity-90 text-white py-2 px-4 rounded-md flex items-center justify-center">
              <i class="fas fa-edit mr-2"></i>
              Edit Profile
            </button>
          </div>
        </div>
      </div>

      <!-- Portfolio and Work History -->
      <div class="grid lg:grid-cols-2 gap-8">
        <!-- Introduction and Portfolio -->
        <div class="card">
          <div class="p-6">
            <!-- Introduction -->
            <div class="mb-8">
              <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold flex items-center gap-2">
                  <i class="fas fa-info-circle text-[#064dac]"></i>
                  Introduction
                </h2>
                <button id="editIntroBtn" class="text-[#CD208B] hover:text-opacity-80 text-sm flex items-center">
                  <i class="fas fa-edit mr-1"></i>
                  Edit
                </button>
              </div>
              <textarea id="introductionText" readonly class="min-h-[100px] resize-none w-full px-3 py-2 border border-gray-300 rounded-md">Welcome to my profile! I'm a dedicated full-stack developer with over 5 years of experience in creating robust web applications.</textarea>
            </div>

            <!-- Portfolio Header -->
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-lg font-semibold">Portfolio</h2>
              <div class="flex gap-2">
                <button id="addPortfolioBtn" class="border border-[#064dac] text-[#064dac] hover:bg-[#064dac] hover:text-white w-8 h-8 rounded-md flex items-center justify-center">
                  <i class="fas fa-plus"></i>
                </button>
                <button id="reorderPortfolioBtn" class="border border-[#064dac] text-[#064dac] hover:bg-[#064dac] hover:text-white w-8 h-8 rounded-md flex items-center justify-center" title="Reorder portfolio projects">
                  <i class="fas fa-sync-alt"></i>
                </button>
              </div>
            </div>

            <!-- Portfolio Tabs -->
            <div class="flex gap-6 mb-6 border-b">
              <button class="portfolio-tab pb-2 font-medium text-black border-b-2 border-black" data-tab="published">
                Published
              </button>
              <button class="portfolio-tab pb-2 font-medium text-gray-600" data-tab="drafts">
                Drafts
              </button>
            </div>

            <!-- Portfolio Grid -->
            <div id="portfolioGrid" class="grid gap-4 mb-6">
              <!-- Portfolio items will be inserted here by JavaScript -->
            </div>

            <!-- Pagination -->
            <div class="flex justify-center items-center gap-2">
              <button class="w-8 h-8 border border-gray-300 rounded-md flex items-center justify-center">
                <i class="fas fa-chevron-left"></i>
              </button>
              <button class="w-8 h-8 bg-[#064dac] text-white rounded-md flex items-center justify-center">
                1
              </button>
              <button class="w-8 h-8 border border-gray-300 rounded-md flex items-center justify-center">
                2
              </button>
              <button class="w-8 h-8 border border-gray-300 rounded-md flex items-center justify-center">
                3
              </button>
              <span class="text-gray-500">...</span>
              <button class="w-8 h-8 border border-gray-300 rounded-md flex items-center justify-center">
                <i class="fas fa-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Work History -->
        <div class="card">
          <div class="p-6">
            <h2 class="text-xl font-semibold mb-6">Work history on Upwork</h2>

            <!-- Summary Section -->
            <div class="bg-gray-50 p-4 rounded-lg mb-6">
              <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold flex items-center gap-2">
                  <i class="fas fa-history text-[#064dac]"></i>
                  Summary
                </h3>
                <div class="flex items-center gap-2">
                  <span class="text-sm text-gray-600">Show on profile</span>
                  <label class="toggle-switch">
                    <input type="checkbox" checked>
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>

              <p class="text-gray-700 text-sm mb-2">
                Expertise lies in Virtual Assistance and Technical Support, specializing in project management, data
                entry, and CRM optimization across diverse industries. Key projects include Social Media Management
                <span class="text-[#064dac] font-medium">[1]</span>, Calendly Integration
                <span class="text-[#064dac] font-medium">[2]</span>, and Marketing Automation
                <span class="text-[#064dac] font-medium">[3]</span>, demonstrating proficiency in tools like
                WordPress, Canva, and Zapier. Delivered measurable outcomes through efficient task coordi...
              </p>

              <button class="text-[#064dac] text-sm font-medium">Show more</button>

              <div class="text-xs text-gray-500 mt-3">
                Generated by Uma, Upwork's Mindful AI, from completed jobs
              </div>
            </div>

            <!-- Skills Used -->
            <div class="mb-6">
              <h3 class="text-sm font-medium text-gray-700 mb-3">Skills used</h3>
              <div class="flex flex-wrap gap-2">
                <span class="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">Customer Service</span>
                <span class="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">Data Entry</span>
                <span class="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">Email Communication</span>
                <span class="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">Landing Page</span>
                <span class="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">Social Media Management</span>
                <span class="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">Virtual Assistance</span>
                <span class="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">Web Design</span>
                <span class="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">Web Development</span>
                <span class="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">Wix</span>
              </div>
            </div>

            <!-- Job Tabs -->
            <div class="border-b mb-6">
              <div class="flex gap-6">
                <button class="job-tab pb-2 font-medium text-black border-b-2 border-black" data-tab="completed">
                  Completed jobs (24)
                </button>
                <button class="job-tab pb-2 font-medium text-gray-600" data-tab="in-progress">
                  In progress (2)
                </button>
              </div>
            </div>

            <!-- Job List -->
            <div class="space-y-8">
              <!-- Job 1 -->
              <div class="border-b pb-6">
                <div class="flex justify-between items-start mb-1">
                  <h3 class="text-[#064dac] font-medium">Test Task 1</h3>
                  <button class="text-gray-500 rounded-full w-8 h-8 flex items-center justify-center hover:bg-gray-100">
                    <i class="fas fa-ellipsis-v"></i>
                  </button>
                </div>
                <div class="text-sm text-gray-500 mb-3">Jul 16, 2024 - Mar 4, 2025</div>
                <div class="text-sm text-gray-700 mb-3">No feedback given</div>
                <div class="flex justify-between items-center">
                  <div class="font-medium">$30.00</div>
                  <div class="text-sm text-gray-500">$30.00/hr</div>
                  <div class="text-sm text-gray-500">1 hour</div>
                </div>
              </div>

              <!-- Job 2 -->
              <div class="pb-6">
                <div class="flex justify-between items-start mb-1">
                  <h3 class="text-[#064dac] font-medium">Technical Support Specialist</h3>
                  <button class="text-gray-500 rounded-full w-8 h-8 flex items-center justify-center hover:bg-gray-100">
                    <i class="fas fa-share-alt text-green-600"></i>
                  </button>
                </div>
                <div class="flex items-center gap-2 mb-1">
                  <div class="flex star-rating">
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                    <i class="fas fa-star"></i>
                  </div>
                  <span class="font-medium">5.0</span>
                  <span class="text-sm text-gray-500">|</span>
                  <span class="text-sm text-gray-500">May 10, 2024 - May 24, 2024</span>
                </div>
                <div class="flex justify-between items-center">
                  <div class="font-medium">$117.50</div>
                  <div class="text-sm text-gray-500">$15.00/hr</div>
                  <div class="text-sm text-gray-500">8 hours</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="bg-[#064dac] text-white py-12 mt-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
          <div>
            <h3 class="font-semibold mb-4">For Clients</h3>
            <div class="space-y-2">
              <a href="#" class="block text-white/80 hover:text-white">How to Hire</a>
              <a href="#" class="block text-white/80 hover:text-white">Marketplace</a>
              <a href="#" class="block text-white/80 hover:text-white">Payroll Services</a>
              <a href="#" class="block text-white/80 hover:text-white">Service Catalog</a>
            </div>
          </div>
          <div>
            <h3 class="font-semibold mb-4">For Geniuses</h3>
            <div class="space-y-2">
              <a href="#" class="block text-white/80 hover:text-white">How It Works?</a>
              <a href="#" class="block text-white/80 hover:text-white">Why Can't I Apply?</a>
              <a href="#" class="block text-white/80 hover:text-white">Direct Contracts</a>
              <a href="#" class="block text-white/80 hover:text-white">Find Mentors</a>
            </div>
          </div>
          <div>
            <h3 class="font-semibold mb-4">Resources</h3>
            <div class="space-y-2">
              <a href="#" class="block text-white/80 hover:text-white">Help & Support</a>
              <a href="#" class="block text-white/80 hover:text-white">News & Events</a>
              <a href="#" class="block text-white/80 hover:text-white">Affiliate Program</a>
            </div>
          </div>
          <div>
            <h3 class="font-semibold mb-4">Company</h3>
            <div class="space-y-2">
              <a href="#" class="block text-white/80 hover:text-white">About Us</a>
              <a href="#" class="block text-white/80 hover:text-white">Contact Us</a>
              <a href="#" class="block text-white/80 hover:text-white">Charity Projects</a>
            </div>
          </div>
        </div>

        <div class="border-t border-white/20 pt-8 flex flex-col md:flex-row justify-between items-center gap-4">
          <div class="flex items-center gap-4">
            <span>Follow Us:</span>
            <div class="flex gap-3">
              <a href="#" class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-[#CD208B] transition-colors">
                <i class="fab fa-facebook-f"></i>
              </a>
              <a href="#" class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-[#CD208B] transition-colors">
                <i class="fab fa-instagram"></i>
              </a>
              <a href="#" class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-[#CD208B] transition-colors">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-[#CD208B] transition-colors">
                <i class="fab fa-youtube"></i>
              </a>
              <a href="#" class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center hover:bg-[#CD208B] transition-colors">
                <i class="fab fa-linkedin-in"></i>
              </a>
            </div>
          </div>
          <p class="text-center">©2025 GigGenius by Genuinely Business Solutions</p>
          <div class="flex gap-4">
            <a href="#" class="text-white/80 hover:text-white">Terms of Service</a>
            <a href="#" class="text-white/80 hover:text-white">Privacy Policy</a>
          </div>
        </div>
      </div>
    </footer>
  </div>

  <!-- Modals -->

  <!-- Professional Summary Modal -->
  <div id="summaryModal" class="modal">
    <div class="modal-content">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Edit Professional Summary</h2>
        <span class="close" data-modal="summaryModal">&times;</span>
      </div>
      <div class="space-y-4">
        <textarea id="summaryTextarea" placeholder="Write your professional summary..." class="min-h-[200px] w-full px-3 py-2 border border-gray-300 rounded-md">Experienced full-stack developer with expertise in modern web technologies. Passionate about creating efficient, scalable solutions that drive business growth.</textarea>
        <div class="flex justify-end gap-2">
          <button class="px-4 py-2 border border-gray-300 rounded-md" data-close="summaryModal">Cancel</button>
          <button id="saveSummaryBtn" class="px-4 py-2 bg-[#064dac] hover:bg-opacity-90 text-white rounded-md">Save Changes</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Introduction Modal -->
  <div id="introModal" class="modal">
    <div class="modal-content">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Edit Introduction</h2>
        <span class="close" data-modal="introModal">&times;</span>
      </div>
      <div class="space-y-4">
        <textarea id="introTextarea" placeholder="Write your introduction (minimum 300 words)..." class="min-h-[200px] w-full px-3 py-2 border border-gray-300 rounded-md">Welcome to my profile! I'm a dedicated full-stack developer with over 5 years of experience in creating robust web applications.</textarea>
        <div class="flex justify-end gap-2">
          <button class="px-4 py-2 border border-gray-300 rounded-md" data-close="introModal">Cancel</button>
          <button id="saveIntroBtn" class="px-4 py-2 bg-[#064dac] hover:bg-opacity-90 text-white rounded-md">Save Changes</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Edit Profile Modal -->
  <div id="profileModal" class="modal">
    <div class="modal-content">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Edit Profile</h2>
        <span class="close" data-modal="profileModal">&times;</span>
      </div>
      <div class="grid lg:grid-cols-3 gap-6">
        <!-- Profile Photo Section -->
        <div class="flex flex-col items-center gap-4">
          <div class="avatar w-32 h-32">
            <img id="profileImage" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='150' height='150' viewBox='0 0 150 150'%3E%3Crect width='150' height='150' fill='%23e5e7eb'/%3E%3Ctext x='75' y='85' text-anchor='middle' fill='%236b7280' font-family='Arial' font-size='48' font-weight='500'%3EJD%3C/text%3E%3C/svg%3E" alt="Profile">
            <div class="avatar-fallback">JD</div>
          </div>
          <button id="uploadPhotoBtn" class="px-4 py-2 border border-[#064dac] text-[#064dac] hover:bg-[#064dac] hover:text-white rounded-md flex items-center">
            <i class="fas fa-upload mr-2"></i>
            Upload Photo
          </button>
          <input id="profileFileInput" type="file" accept="image/*" class="hidden">
          <p class="text-xs text-gray-500">Maximum 2MB</p>
        </div>

        <!-- Profile Details -->
        <div class="lg:col-span-2 space-y-4">
          <div>
            <label class="text-sm font-medium">Email</label>
            <input type="email" id="emailInput" value="<EMAIL>" readonly class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md">
          </div>

          <div>
            <label class="text-sm font-medium">Mobile No.</label>
            <input type="tel" id="mobileInput" value="+1234567890" class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:border-[#064dac]">
          </div>

          <div>
            <label class="text-sm font-medium">Position</label>
            <input type="text" id="positionInput" value="Full Stack Developer" class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:border-[#064dac]">
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium">Expertise Level</label>
              <select id="expertiseSelect" class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md">
                <option value="Expert" selected>Expert</option>
                <option value="Intermediate">Intermediate</option>
                <option value="Beginner">Beginner</option>
              </select>
            </div>

            <div>
              <label class="text-sm font-medium">Rate per Hour (USD)</label>
              <input type="number" id="rateInput" value="50" class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:border-[#064dac]">
            </div>
          </div>

          <div>
            <label class="text-sm font-medium">Availability</label>
            <input type="text" id="availabilityInput" value="Full-time" placeholder="e.g., Part-Time, Full-Time, Weekends only" class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:border-[#064dac]">
          </div>

          <div>
            <label class="text-sm font-medium">Country</label>
            <select id="countrySelect" class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="United States" selected>United States</option>
              <option value="United Kingdom">United Kingdom</option>
              <option value="Canada">Canada</option>
              <option value="Australia">Australia</option>
              <option value="Germany">Germany</option>
              <option value="France">France</option>
              <option value="Philippines">Philippines</option>
              <option value="India">India</option>
            </select>
          </div>

          <div class="flex justify-end gap-2 pt-4">
            <button class="px-4 py-2 border border-gray-300 rounded-md" data-close="profileModal">Cancel</button>
            <button id="saveProfileBtn" class="px-4 py-2 bg-[#064dac] hover:bg-opacity-90 text-white rounded-md">Save Changes</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Portfolio Modal - Upwork Style -->
  <div id="portfolioAddModal" class="modal">
    <div class="modal-content">
      <div class="flex justify-between items-center pb-4 border-b">
        <h2 class="text-xl font-semibold">Add a new portfolio project</h2>
        <span class="close" data-modal="portfolioAddModal">&times;</span>
      </div>

      <div class="space-y-6 pt-4">
        <p class="text-sm text-gray-600">All fields are required unless otherwise indicated.</p>

        <!-- Project Title -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Project title</label>
          <input type="text" id="projectTitleInput" placeholder="Enter a brief but descriptive title" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:border-[#064dac]" maxlength="70">
          <div class="text-right text-xs text-gray-500">
            <span id="projectTitleCount">70</span> characters left
          </div>
        </div>

        <!-- Your Role -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">
            Your role <span class="text-gray-500 font-normal">(optional)</span>
          </label>
          <input type="text" id="roleInput" placeholder="e.g. Front-end engineer or Marketing analyst" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:border-[#064dac]" maxlength="100">
          <div class="text-right text-xs text-gray-500">
            <span id="roleCount">100</span> characters left
          </div>
        </div>

        <!-- Project Description -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Project description</label>
          <textarea id="projectDescInput" placeholder="Briefly describe the project's goals, your solution and the impact you made here" class="min-h-[100px] w-full px-3 py-2 border border-gray-300 rounded-md focus:border-[#064dac]" maxlength="500"></textarea>
          <div class="text-right text-xs text-gray-500">
            <span id="projectDescCount">500</span> characters left
          </div>
        </div>

        <!-- Add Content Section -->
        <div class="space-y-2">
          <div class="border-2 border-dashed border-green-300 rounded-lg p-8 text-center bg-green-50/30">
            <div class="flex justify-center gap-4 mb-4">
              <button class="h-12 w-12 rounded-full border-2 border-green-500 text-green-600 hover:bg-green-100 flex items-center justify-center" id="addImageBtn">
                <i class="fas fa-image"></i>
              </button>
              <button class="h-12 w-12 rounded-full border-2 border-green-500 text-green-600 hover:bg-green-100 flex items-center justify-center">
                <i class="fas fa-video"></i>
              </button>
              <button class="h-12 w-12 rounded-full border-2 border-green-500 text-green-600 hover:bg-green-100 flex items-center justify-center">
                <i class="fas fa-font"></i>
              </button>
              <button class="h-12 w-12 rounded-full border-2 border-green-500 text-green-600 hover:bg-green-100 flex items-center justify-center">
                <i class="fas fa-link"></i>
              </button>
              <button class="h-12 w-12 rounded-full border-2 border-green-500 text-green-600 hover:bg-green-100 flex items-center justify-center">
                <i class="fas fa-file-alt"></i>
              </button>
              <button class="h-12 w-12 rounded-full border-2 border-green-500 text-green-600 hover:bg-green-100 flex items-center justify-center">
                <i class="fas fa-music"></i>
              </button>
            </div>
            <p class="text-gray-600 font-medium">Add content</p>
          </div>
          <input id="portfolioFileInput" type="file" accept="image/*,video/*" class="hidden">
        </div>

        <!-- Skills and Deliverables -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">Skills and deliverables</label>
          <input type="text" id="skillsInput" placeholder="Type to add skills relevant to this project" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:border-[#064dac]">
          <div class="text-right text-xs text-gray-500">
            <span id="skillsCount">5</span> skills left
          </div>
        </div>

        <!-- Related Upwork Job -->
        <div class="space-y-2">
          <label class="text-sm font-medium text-gray-900">
            Related Upwork job <span class="text-gray-500 font-normal">(optional)</span>
          </label>
          <input type="text" id="relatedJobInput" placeholder="Search a related job" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:border-[#064dac]">
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end gap-3 pt-6 border-t">
          <button class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-md" data-close="portfolioAddModal">
            Save as draft
          </button>
          <button id="addPortfolioSubmitBtn" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md">
            Next: Preview
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Reorder Portfolio Modal -->
  <div id="portfolioReorderModal" class="modal">
    <div class="modal-content">
      <div class="flex justify-between items-center pb-4 border-b">
        <h2 class="text-xl font-semibold">Reorder portfolio projects</h2>
        <span class="close" data-modal="portfolioReorderModal">&times;</span>
      </div>

      <div id="reorderProjectsList" class="space-y-4 pt-4">
        <!-- Reorder items will be inserted here by JavaScript -->
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end gap-3 pt-6 border-t mt-4">
        <button class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-md" data-close="portfolioReorderModal">
          Cancel
        </button>
        <button id="saveReorderBtn" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md">
          Save
        </button>
      </div>
    </div>
  </div>

  <script>
    // Portfolio projects data
    const portfolioProjects = [
      {
        id: "1",
        title: "Bryan's Handyman & Construction LLC",
        image: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23e5e7eb'/%3E%3Ctext x='150' y='110' text-anchor='middle' fill='%236b7280' font-family='Arial' font-size='16'%3EProject Image%3C/text%3E%3C/svg%3E",
        order: 1,
      },
      {
        id: "2",
        title: "Virtual Assistant",
        image: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23e5e7eb'/%3E%3Ctext x='150' y='110' text-anchor='middle' fill='%236b7280' font-family='Arial' font-size='16'%3EProject Image%3C/text%3E%3C/svg%3E",
        order: 2,
      },
      {
        id: "3",
        title: "Graphics Design and Web Developer",
        image: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23e5e7eb'/%3E%3Ctext x='150' y='110' text-anchor='middle' fill='%236b7280' font-family='Arial' font-size='16'%3EProject Image%3C/text%3E%3C/svg%3E",
        order: 3,
      },
      {
        id: "4",
        title: "GigGenius Web and App Development",
        image: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23e5e7eb'/%3E%3Ctext x='150' y='110' text-anchor='middle' fill='%236b7280' font-family='Arial' font-size='16'%3EProject Image%3C/text%3E%3C/svg%3E",
        order: 4,
      },
      {
        id: "5",
        title: "Bryan's Handyman and Construction LLC",
        image: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23e5e7eb'/%3E%3Ctext x='150' y='110' text-anchor='middle' fill='%236b7280' font-family='Arial' font-size='16'%3EProject Image%3C/text%3E%3C/svg%3E",
        order: 5,
      },
      {
        id: "6",
        title: "SMB Paralegal Services",
        image: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200' viewBox='0 0 300 200'%3E%3Crect width='300' height='200' fill='%23e5e7eb'/%3E%3Ctext x='150' y='110' text-anchor='middle' fill='%236b7280' font-family='Arial' font-size='16'%3EProject Image%3C/text%3E%3C/svg%3E",
        order: 6,
      },
    ];

    let reorderProjects = [];

    // Initialize the application
    document.addEventListener('DOMContentLoaded', function() {
      // Show loading screen first
      showLoadingScreen();
    });

    // Loading screen functionality
    function showLoadingScreen() {
      const loadingScreen = document.getElementById('loadingScreen');
      const mainContent = document.getElementById('mainContent');
      const progressBar = document.getElementById('progressBar');
      
      // Hide main content initially
      mainContent.classList.remove('show');
      
      // Simulate loading progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          
          // Hide loading screen and show main content after loading completes
          setTimeout(() => {
            loadingScreen.style.display = 'none';
            mainContent.classList.add('show');
            initializeMainApp();
          }, 500);
        }
        
        progressBar.style.width = progress + '%';
      }, 200);
      
      // Auto hide loading screen after 3 seconds regardless of progress
      setTimeout(() => {
        if (loadingScreen.style.display !== 'none') {
          loadingScreen.style.display = 'none';
          mainContent.classList.add('show');
          initializeMainApp();
        }
      }, 3000);
    }

    // Initialize main application
    function initializeMainApp() {
      // Render portfolio projects
      renderPortfolioProjects();
      
      // Add event listeners
      setupEventListeners();
      
      // Setup character counters
      setupCharacterCounters();
    }

    // DOM Elements
    const notificationBtn = document.getElementById('notificationBtn');
    const notificationDropdown = document.getElementById('notificationDropdown');
    const profileBtn = document.getElementById('profileBtn');
    const profileDropdown = document.getElementById('profileDropdown');
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const videoContainer = document.getElementById('videoContainer');
    const profileVideo = document.getElementById('profileVideo');
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    const portfolioGrid = document.getElementById('portfolioGrid');
    const reorderProjectsList = document.getElementById('reorderProjectsList');
    
    // Modal buttons
    const editSummaryBtn = document.getElementById('editSummaryBtn');
    const editIntroBtn = document.getElementById('editIntroBtn');
    const editProfileBtn = document.getElementById('editProfileBtn');
    const addPortfolioBtn = document.getElementById('addPortfolioBtn');
    const reorderPortfolioBtn = document.getElementById('reorderPortfolioBtn');
    
    // Modal elements
    const summaryModal = document.getElementById('summaryModal');
    const introModal = document.getElementById('introModal');
    const profileModal = document.getElementById('profileModal');
    const portfolioAddModal = document.getElementById('portfolioAddModal');
    const portfolioReorderModal = document.getElementById('portfolioReorderModal');
    
    // Form elements
    const summaryTextarea = document.getElementById('summaryTextarea');
    const introTextarea = document.getElementById('introTextarea');
    const professionalSummary = document.getElementById('professionalSummary');
    const introductionText = document.getElementById('introductionText');
    const uploadPhotoBtn = document.getElementById('uploadPhotoBtn');
    const profileFileInput = document.getElementById('profileFileInput');
    const portfolioFileInput = document.getElementById('portfolioFileInput');
    const addImageBtn = document.getElementById('addImageBtn');
    
    // Save buttons
    const saveSummaryBtn = document.getElementById('saveSummaryBtn');
    const saveIntroBtn = document.getElementById('saveIntroBtn');
    const saveProfileBtn = document.getElementById('saveProfileBtn');
    const addPortfolioSubmitBtn = document.getElementById('addPortfolioSubmitBtn');
    const saveReorderBtn = document.getElementById('saveReorderBtn');
    
    // Character count elements
    const projectTitleInput = document.getElementById('projectTitleInput');
    const roleInput = document.getElementById('roleInput');
    const projectDescInput = document.getElementById('projectDescInput');
    const skillsInput = document.getElementById('skillsInput');
    const projectTitleCount = document.getElementById('projectTitleCount');
    const roleCount = document.getElementById('roleCount');
    const projectDescCount = document.getElementById('projectDescCount');
    const skillsCount = document.getElementById('skillsCount');
    
    // Tab elements
    const portfolioTabs = document.querySelectorAll('.portfolio-tab');
    const jobTabs = document.querySelectorAll('.job-tab');

    // Render portfolio projects
    function renderPortfolioProjects() {
      if (!portfolioGrid) return;
      
      portfolioGrid.innerHTML = '';
      
      // Sort projects by order and take first 2
      const sortedProjects = [...portfolioProjects]
        .sort((a, b) => a.order - b.order)
        .slice(0, 2);
      
      sortedProjects.forEach(project => {
        const projectCard = document.createElement('div');
        projectCard.className = 'card overflow-hidden cursor-pointer hover:shadow-md transition-shadow';
        
        projectCard.innerHTML = `
          <div class="aspect-video overflow-hidden">
            <img src="${project.image}" alt="${project.title}" class="w-full h-full object-cover hover:scale-105 transition-transform">
          </div>
          <div class="p-4">
            <h3 class="font-semibold text-green-600">${project.title}</h3>
          </div>
        `;
        
        portfolioGrid.appendChild(projectCard);
      });
    }

    // Render reorder projects list
    function renderReorderProjects() {
      if (!reorderProjectsList) return;
      
      reorderProjectsList.innerHTML = '';
      
      reorderProjects.forEach((project, index) => {
        const projectItem = document.createElement('div');
        projectItem.className = 'flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 transition-colors';
        projectItem.dataset.id = project.id;
        
        projectItem.innerHTML = `
          <div class="cursor-move text-gray-400 hover:text-gray-600 draggable-item">
            <i class="fas fa-grip-vertical"></i>
          </div>
          <div class="w-16 h-12 rounded overflow-hidden flex-shrink-0">
            <img src="${project.image}" alt="${project.title}" class="w-full h-full object-cover">
          </div>
          <div class="flex-1">
            <h3 class="font-medium text-gray-900">${project.title}</h3>
          </div>
          <div class="flex flex-col gap-1">
            <button class="move-up h-8 w-8 text-gray-400 hover:text-gray-600 ${index === 0 ? 'opacity-30 cursor-not-allowed' : ''}" ${index === 0 ? 'disabled' : ''}>
              <i class="fas fa-arrow-up"></i>
            </button>
            <button class="move-down h-8 w-8 text-gray-400 hover:text-gray-600 ${index === reorderProjects.length - 1 ? 'opacity-30 cursor-not-allowed' : ''}" ${index === reorderProjects.length - 1 ? 'disabled' : ''}>
              <i class="fas fa-arrow-down"></i>
            </button>
          </div>
        `;
        
        reorderProjectsList.appendChild(projectItem);
      });
      
      // Add event listeners for move buttons
      document.querySelectorAll('.move-up').forEach(btn => {
        btn.addEventListener('click', function() {
          const projectItem = this.closest('[data-id]');
          const projectId = projectItem.dataset.id;
          moveProject(projectId, 'up');
        });
      });
      
      document.querySelectorAll('.move-down').forEach(btn => {
        btn.addEventListener('click', function() {
          const projectItem = this.closest('[data-id]');
          const projectId = projectItem.dataset.id;
          moveProject(projectId, 'down');
        });
      });
    }

    // Move project up or down
    function moveProject(projectId, direction) {
      const currentIndex = reorderProjects.findIndex(p => p.id === projectId);
      if (
        (direction === 'up' && currentIndex === 0) ||
        (direction === 'down' && currentIndex === reorderProjects.length - 1)
      ) {
        return;
      }
      
      const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
      
      // Swap the projects
      [reorderProjects[currentIndex], reorderProjects[targetIndex]] = 
      [reorderProjects[targetIndex], reorderProjects[currentIndex]];
      
      // Re-render the list
      renderReorderProjects();
    }

    // Setup event listeners
    function setupEventListeners() {
      // Dropdown toggles
      if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
          notificationDropdown.classList.toggle('hidden');
          profileDropdown.classList.add('hidden');
        });
      }
      
      if (profileBtn) {
        profileBtn.addEventListener('click', function() {
          profileDropdown.classList.toggle('hidden');
          notificationDropdown.classList.add('hidden');
        });
      }
      
      // Close dropdowns when clicking outside
      document.addEventListener('click', function(event) {
        if (notificationBtn && notificationDropdown && !notificationBtn.contains(event.target) && !notificationDropdown.contains(event.target)) {
          notificationDropdown.classList.add('hidden');
        }
        
        if (profileBtn && profileDropdown && !profileBtn.contains(event.target) && !profileDropdown.contains(event.target)) {
          profileDropdown.classList.add('hidden');
        }
      });
      
      // Video fullscreen toggle
      if (fullscreenBtn && videoContainer) {
        fullscreenBtn.addEventListener('click', function() {
          if (videoContainer.classList.contains('aspect-video')) {
            // Enter fullscreen
            videoContainer.classList.remove('aspect-video');
            videoContainer.classList.add('fullscreen-video');
            fullscreenBtn.innerHTML = '<i class="fas fa-compress mr-2"></i> Exit Fullscreen';
          } else {
            // Exit fullscreen
            videoContainer.classList.add('aspect-video');
            videoContainer.classList.remove('fullscreen-video');
            fullscreenBtn.innerHTML = '<i class="fas fa-expand mr-2"></i> Fullscreen';
          }
        });
      }
      
      // Modal open buttons
      if (editSummaryBtn && summaryModal) {
        editSummaryBtn.addEventListener('click', function() {
          summaryModal.style.display = 'block';
        });
      }
      
      if (editIntroBtn && introModal) {
        editIntroBtn.addEventListener('click', function() {
          introModal.style.display = 'block';
        });
      }
      
      if (editProfileBtn && profileModal) {
        editProfileBtn.addEventListener('click', function() {
          profileModal.style.display = 'block';
        });
      }
      
      if (addPortfolioBtn && portfolioAddModal) {
        addPortfolioBtn.addEventListener('click', function() {
          portfolioAddModal.style.display = 'block';
        });
      }
      
      if (reorderPortfolioBtn && portfolioReorderModal) {
        reorderPortfolioBtn.addEventListener('click', function() {
          reorderProjects = [...portfolioProjects].sort((a, b) => a.order - b.order);
          renderReorderProjects();
          portfolioReorderModal.style.display = 'block';
        });
      }
      
      // Modal close buttons
      document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
          const modalId = this.dataset.modal;
          const modal = document.getElementById(modalId);
          if (modal) modal.style.display = 'none';
        });
      });
      
      document.querySelectorAll('[data-close]').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
          const modalId = this.dataset.close;
          const modal = document.getElementById(modalId);
          if (modal) modal.style.display = 'none';
        });
      });
      
      // Close modal when clicking outside
      window.addEventListener('click', function(event) {
        document.querySelectorAll('.modal').forEach(modal => {
          if (event.target === modal) {
            modal.style.display = 'none';
          }
        });
      });
      
      // File upload buttons
      if (uploadPhotoBtn && profileFileInput) {
        uploadPhotoBtn.addEventListener('click', function() {
          profileFileInput.click();
        });
      }
      
      if (addImageBtn && portfolioFileInput) {
        addImageBtn.addEventListener('click', function() {
          portfolioFileInput.click();
        });
      }
      
      // File input change handlers
      if (profileFileInput) {
        profileFileInput.addEventListener('change', function(e) {
          const file = e.target.files[0];
          if (file) {
            if (!file.type.startsWith('image/')) {
              alert('Please select an image file');
              return;
            }
            
            if (file.size > 2 * 1024 * 1024) {
              alert('File size must be less than 2MB');
              return;
            }
            
            const reader = new FileReader();
            reader.onload = function(e) {
              const profileImage = document.getElementById('profileImage');
              if (profileImage) profileImage.src = e.target.result;
            };
            reader.readAsDataURL(file);
          }
        });
      }
      
      // Save buttons
      if (saveSummaryBtn && professionalSummary && summaryTextarea && summaryModal) {
        saveSummaryBtn.addEventListener('click', function() {
          professionalSummary.textContent = summaryTextarea.value;
          summaryModal.style.display = 'none';
          alert('Professional summary updated successfully!');
        });
      }
      
      if (saveIntroBtn && introductionText && introTextarea && introModal) {
        saveIntroBtn.addEventListener('click', function() {
          introductionText.value = introTextarea.value;
          introModal.style.display = 'none';
          alert('Introduction updated successfully!');
        });
      }
      
      if (saveProfileBtn && profileModal) {
        saveProfileBtn.addEventListener('click', function() {
          // Update profile data
          profileModal.style.display = 'none';
          alert('Profile updated successfully!');
        });
      }
      
      if (addPortfolioSubmitBtn && portfolioAddModal && projectTitleInput && projectDescInput) {
        addPortfolioSubmitBtn.addEventListener('click', function() {
          if (!projectTitleInput.value || !projectDescInput.value) {
            alert('Please fill in the required fields');
            return;
          }
          
          portfolioAddModal.style.display = 'none';
          
          // Reset form
          projectTitleInput.value = '';
          if (roleInput) roleInput.value = '';
          projectDescInput.value = '';
          if (skillsInput) skillsInput.value = '';
          const relatedJobInput = document.getElementById('relatedJobInput');
          if (relatedJobInput) relatedJobInput.value = '';
          
          alert('Portfolio project added successfully!');
        });
      }
      
      if (saveReorderBtn && portfolioReorderModal) {
        saveReorderBtn.addEventListener('click', function() {
          // Update the order property for each project
          reorderProjects.forEach((project, index) => {
            const originalProject = portfolioProjects.find(p => p.id === project.id);
            if (originalProject) {
              originalProject.order = index + 1;
            }
          });
          
          portfolioReorderModal.style.display = 'none';
          renderPortfolioProjects();
          alert('Portfolio order updated successfully!');
        });
      }
      
      // Tab switching
      portfolioTabs.forEach(tab => {
        tab.addEventListener('click', function() {
          portfolioTabs.forEach(t => {
            t.classList.remove('text-black', 'border-b-2', 'border-black');
            t.classList.add('text-gray-600');
          });
          
          this.classList.add('text-black', 'border-b-2', 'border-black');
          this.classList.remove('text-gray-600');
        });
      });
      
      jobTabs.forEach(tab => {
        tab.addEventListener('click', function() {
          jobTabs.forEach(t => {
            t.classList.remove('text-black', 'border-b-2', 'border-black');
            t.classList.add('text-gray-600');
          });
          
          this.classList.add('text-black', 'border-b-2', 'border-black');
          this.classList.remove('text-gray-600');
        });
      });
    }

    // Setup character counters
    function setupCharacterCounters() {
      if (projectTitleInput && projectTitleCount) {
        projectTitleInput.addEventListener('input', function() {
          const remaining = 70 - this.value.length;
          projectTitleCount.textContent = remaining;
        });
      }
      
      if (roleInput && roleCount) {
        roleInput.addEventListener('input', function() {
          const remaining = 100 - this.value.length;
          roleCount.textContent = remaining;
        });
      }
      
      if (projectDescInput && projectDescCount) {
        projectDescInput.addEventListener('input', function() {
          const remaining = 500 - this.value.length;
          projectDescCount.textContent = remaining;
        });
      }
      
      if (skillsInput && skillsCount) {
        skillsInput.addEventListener('input', function() {
          const skillArray = this.value.split(',').filter(skill => skill.trim().length > 0);
          const remaining = 5 - skillArray.length;
          skillsCount.textContent = remaining;
        });
      }
    }
  </script>
</body>
</html>
